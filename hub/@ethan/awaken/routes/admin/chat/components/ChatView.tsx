"use client";

import { useEffect, useState, useRef, useMemo } from "npm:react@canary";
import { Button, ScrollArea, Text, X } from "@reframe/ui/main.tsx";
import { BackIcon, RefreshIcon, BrainIcon, SparklesIcon, ThreeSparklesIcon, TagIcon } from "../../../../lib/icons.tsx";
import { getCoachUserConversation, summarizeUserConversation, markConversationMessagesSeenByCoach, updateUserTags } from "../../../admin/actions/admin-actions.ts";
import { sendCoachMessage } from "../../../admin/actions/admin-actions.ts";
import { UserThread } from "./UserThread.tsx";
import { ThreadIcon } from "../../../../lib/icons.tsx";
import { TagEditor } from "./TagEditor.tsx";

interface User {
  channelId: number;
  userName: string;
  userEmail: string;
  userImage?: string | null;
  tags?: string[];

  hasUnseen: {
    regularMessage: boolean;
    userThreadMessage: boolean;
  };

  lastMessage: {
    content: string;
    date: string;
    sender: string;
    type: string;
  };
}

interface Message {
  id: string;
  channelId: number;
  sender: string;
  content: string;
  date: string;
  status?: string;
  messageType: string;
  audio?: string;
  coachName: string;
  seenByCoach?: boolean;
}

interface ChatViewProps {
  user: User;
  coach: {
    name: string;
    email: string;
    description?: string;
    metadata?: string;
  };
  onBack: () => void;
  isMobile?: boolean;

  /**
   * Called after the coach	opens the user thread overlay. Used by the parent
   * component (AdminChat) to clear the `hasUnseen` indicator in the user list
   * once the coach actively looks at the thread.
   */
  onThreadOpened?: (channelId: number) => void;
  onTagsUpdated?: (channelId: number, tags: string[]) => void;
}

// In-memory cache that persists for the session
const messageCache = new Map<string, { 
  messages: Message[], 
  hasMore: boolean,
  oldestMessageDate?: string 
}>();

export const ChatView: React.FC<ChatViewProps> = ({ user, coach, onBack, isMobile = false, onThreadOpened, onTagsUpdated }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputContainerRef = useRef<HTMLDivElement>(null);
  const handleInputFocus = () => {
    // Ensure latest messages & input field are visible when soft-keyboard appears
    requestAnimationFrame(() => {
      if (scrollAreaRef.current) {
        scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
      }
    });
  };

  // For scroll position preservation and behavior control
  const [anchorMessageId, setAnchorMessageId] = useState<string | null>(null);
  const [scrollBehavior, setScrollBehavior] = useState<'toBottom' | 'preserve' | 'none'>('toBottom');

  // Summary state
  const [summaryLoading, setSummaryLoading] = useState(false);
  const [summary, setSummary] = useState<string | null>(null);
  const [showSummary, setShowSummary] = useState(false);
  const [summaryTokenInfo, setSummaryTokenInfo] = useState<{
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
    responseTime: string;
  } | null>(null);

  // New message input state for coach direct messages
  const [newMessage, setNewMessage] = useState<string>("");
  // Thread overlay state
  const [showUserThread, setShowUserThread] = useState(false);
  const [userThreadMessages, setUserThreadMessages] = useState<Message[]>([]);
  const [threadAnchorId, setThreadAnchorId] = useState<string | null>(null);
  const [sending, setSending] = useState<boolean>(false);

  // Tag editor state
  const [showTagEditor, setShowTagEditor] = useState(false);

  const openUserThread = (clickedMessage: Message) => {
    const threadMsgs = messages.filter(m => m.messageType === 'coach_message');
    setUserThreadMessages(threadMsgs);
    setThreadAnchorId(clickedMessage.id);

    // Mark unseen user thread messages as seen locally so UI updates instantly
    setMessages(prev => prev.map(m => (
      m.messageType === 'coach_message' && m.sender === 'user' && !m.seenByCoach
        ? { ...m, seenByCoach: true }
        : m
    )));

    setShowUserThread(true);

    // Inform parent so it can update the unseen indicator
    onThreadOpened?.(user.channelId);
  };

  const openThreadFromHeader = () => {
    const threadMsgs = messages.filter(m => m.messageType === 'coach_message');
    setUserThreadMessages(threadMsgs);
    setThreadAnchorId(null);

    // Mark unseen user thread messages as seen locally
    setMessages(prev => prev.map(m => (
      m.messageType === 'coach_message' && m.sender === 'user' && !m.seenByCoach
        ? { ...m, seenByCoach: true }
        : m
    )));

    setShowUserThread(true);

    // Inform parent as well – we might be opening the thread from the header
    onThreadOpened?.(user.channelId);
  };

  // Track which summary messages are expanded (by message ID)
  const [openSummaryIds, setOpenSummaryIds] = useState<Set<string>>(new Set());

  const hasUnseenThread = useMemo(() => messages.some(m => m.messageType === 'coach_message' && m.sender === 'user' && (!m.seenByCoach)), [messages]);

  const MESSAGES_PER_PAGE = 25;
  const getCacheKey = (channelId: number, coachName: string) => `${channelId}-${coachName}`;

  console.log("COACH EMAIL IN CHAT VIEW", coach);
  console.log("USER IN CHAT VIEW", user);

  const fetchMessages = async (beforeDate?: string, isLoadMore = false) => {
    try {
      if (!isLoadMore) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }
      setError(null);
      
      const cacheKey = getCacheKey(user.channelId, coach.name);
      
      // For initial load, check if we have cached data
      if (!beforeDate && !isLoadMore) {
        const cached = messageCache.get(cacheKey);
        if (cached && cached.messages.length > 0) {
          setMessages(cached.messages);
          setHasMore(cached.hasMore);
          setLoading(false);
          return;
        }
      }

      // AnchorMessageId and scrollBehavior are now set by the caller (handleScroll) before fetchMessages
      
      const conversationData = await getCoachUserConversation(
        user.channelId.toString(),
        coach,
        MESSAGES_PER_PAGE,
        beforeDate
      );

      console.log("CONVERSATION DATA", conversationData);
      
      // Handle the response structure
      const newMessages = Array.isArray(conversationData) ? conversationData : conversationData.messages || [];
      
      // Sort messages by date (oldest first for proper chat display)
      const sortedMessages = newMessages.sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      if (isLoadMore) {
        // Prepend older messages to existing ones
        const updatedMessages = [...sortedMessages, ...messages];
        setMessages(updatedMessages);
        
        // Update cache
        messageCache.set(cacheKey, {
          messages: updatedMessages,
          hasMore: sortedMessages.length === MESSAGES_PER_PAGE,
          oldestMessageDate: sortedMessages.length > 0 ? sortedMessages[0].date : undefined
        });
      } else {
        // Set initial messages
        setMessages(sortedMessages);
        
        // Update cache
        messageCache.set(cacheKey, {
          messages: sortedMessages,
          hasMore: sortedMessages.length === MESSAGES_PER_PAGE,
          oldestMessageDate: sortedMessages.length > 0 ? sortedMessages[0].date : undefined
        });
      }
      
      setHasMore(sortedMessages.length === MESSAGES_PER_PAGE);
      
    } catch (err: any) {
      console.error("Error fetching messages:", err);
      setError(err.message || "Failed to load messages");
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Load more messages when scrolling to top
  const handleScroll = () => {
    if (loadingMore || !hasMore || !scrollAreaRef.current) return;

    const { scrollTop } = scrollAreaRef.current;
    if (scrollTop < 100) {
      const oldestMessage = messages[0];
      if (oldestMessage) {
        console.log('[ChatView] handleScroll: Setting scrollBehavior to PRESERVE. Anchor:', oldestMessage.id);
        setAnchorMessageId(oldestMessage.id);
        setScrollBehavior('preserve');
        fetchMessages(oldestMessage.date, true);
      }
    }
  };

  useEffect(() => {
    const scrollContainer = scrollAreaRef.current;
    console.log('[ChatView] Scroll useEffect triggered. Behavior:', scrollBehavior, 'Anchor:', anchorMessageId, 'Loading:', loading, 'LoadingMore:', loadingMore, 'MsgCount:', messages.length);
    if (!scrollContainer || loading || loadingMore) {
      console.log('[ChatView] Scroll useEffect: Bailing out (loading or no container).');
      return;
    }

    if (scrollBehavior === 'preserve' && anchorMessageId && messages.length > 0) {
      let targetMessageIdForScroll: string | null = null;
      const anchorIndexInNewMessages = messages.findIndex(m => m.id === anchorMessageId);

      if (anchorIndexInNewMessages !== -1) {
        const targetMessageIndex = Math.max(0, anchorIndexInNewMessages - 2);
        // Fallback to the first message if the target index is out of bounds or message doesn't exist
        targetMessageIdForScroll = messages[targetMessageIndex]?.id || messages[0]?.id;
      } else if (messages.length > 0) {
        // Fallback if anchor not found in current messages (e.g., after a full refresh)
        targetMessageIdForScroll = messages[0].id;
      }

      if (targetMessageIdForScroll) {
        requestAnimationFrame(() => {
          const targetElement = scrollContainer.querySelector(`[data-message-id="${targetMessageIdForScroll}"]`) as HTMLElement;
          if (targetElement) {
            const currentScrollTop = scrollContainer.scrollTop;
            const targetElementRect = targetElement.getBoundingClientRect();
            const scrollContainerRect = scrollContainer.getBoundingClientRect();
            const newScrollTop = currentScrollTop + (targetElementRect.top - scrollContainerRect.top);
            console.log('[ChatView] Scroll PRESERVE: Scrolling to element', targetMessageIdForScroll, 'New scrollTop:', newScrollTop);
            scrollContainer.scrollTop = newScrollTop;
          } else {
            console.log('[ChatView] Scroll PRESERVE: Target element for scroll not found:', targetMessageIdForScroll);
          }
        });
      } else {
        console.log('[ChatView] Scroll PRESERVE: No targetMessageIdForScroll determined.');
      }
      setScrollBehavior('none'); // Reset behavior after scrolling
    } else if (scrollBehavior === 'toBottom' && messages.length > 0) {
      requestAnimationFrame(() => {
        if (scrollContainer) {
          console.log('[ChatView] Scroll TO_BOTTOM: Scrolling to scrollHeight:', scrollContainer.scrollHeight);
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }
      });
      setScrollBehavior('none'); // Reset behavior after scrolling
    } else {
      console.log('[ChatView] Scroll useEffect: No action taken. Behavior:', scrollBehavior, 'MsgCount:', messages.length);
    }
  }, [messages, loading, loadingMore, scrollBehavior, anchorMessageId, scrollAreaRef]); // scrollAreaRef itself, not .current

  // Fetch messages on mount and when user/coach changes
  useEffect(() => {
    console.log('[ChatView] User/Coach changed. ChannelId:', user.channelId, 'Coach:', coach.name, 'Setting scrollBehavior to TO_BOTTOM.');
    setMessages([]);
    setHasMore(true);
    setError(null);
    setScrollBehavior('toBottom');
    fetchMessages();

    // Mark messages as seen by coach
    markConversationMessagesSeenByCoach(user.channelId.toString(), coach.name).catch(err => {
      console.error('[ChatView] Failed to mark conversation messages as seen:', err);
    });
  }, [user.channelId, coach.name]); // Use coach.name or a stable coach identifier

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const formatMessageDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString([], { 
      weekday: 'short',
      month: 'short', 
      day: 'numeric',
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const handleRefresh = () => {
    console.log('[ChatView] handleRefresh: Setting scrollBehavior to TO_BOTTOM.');
    messageCache.delete(getCacheKey(user.channelId, coach.name));
    setMessages([]);
    setScrollBehavior('toBottom');
    fetchMessages(undefined, false);
  };

  const generateSummary = async () => {
    if (summaryLoading) return;
    setSummary(null);
    setSummaryTokenInfo(null);
    setSummaryLoading(true);
    try {
      const result = await summarizeUserConversation(user.channelId, coach.name);
      setSummary(result.summary);
      setSummaryTokenInfo(result.tokenInfo);
      setShowSummary(true);

      // Refresh messages so the new summary message appears in the timeline
      handleRefresh();
    } catch (err: any) {
      console.error('Error generating summary:', err);
      setSummary('[Error generating summary. Please try again.]');
      setShowSummary(true);
    } finally {
      setSummaryLoading(false);
    }
  };

  const handleSummaryButton = () => {
    if (summary) {
      // Toggle summary overlay
      setShowSummary(true);
    } else {
      generateSummary();
    }
  };

  const toggleSummaryVisibility = (id: string) => {
    const willOpen = !openSummaryIds.has(id);

    // Update the set of open summaries
    setOpenSummaryIds(prev => {
      const next = new Set(prev);
      if (next.has(id)) {
        next.delete(id);
      } else {
        next.add(id);
      }
      return next;
    });

    // If the summary is being opened, ensure it scrolls into view after DOM update
    if (willOpen) {
      // Give React time to render the expanded content
      setTimeout(() => {
        const scrollContainer = scrollAreaRef.current;
        if (!scrollContainer) return;

        const elem = scrollContainer.querySelector(`[data-message-id="${id}"]`) as HTMLElement | null;
        if (elem) {
          elem.scrollIntoView({ behavior: 'smooth', block: 'end' });
        } else {
          // Fallback: scroll to bottom
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }
      }, 50); // slight delay to allow layout update
    }
  };

  const handleSendMessage = async (message: string) => {
    const content = message.trim();
    if (!content) return;

    setSending(true);
    try {
      const saved = await sendCoachMessage(
        user.channelId,
        { name: coach.name, email: coach.email },
        content,
      );

      // Optimistically append the new message to UI without refetch
      setMessages(prev => [...prev, saved]);
      setNewMessage("");

      // Scroll to bottom to show the new message
      requestAnimationFrame(() => {
        if (scrollAreaRef.current) {
          scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
        }
      });
    } catch (err: any) {
      console.error('Error sending message:', err);
      setError(err.message || 'Failed to send message');
    } finally {
      setSending(false);
    }
  };

  if (loading && messages.length === 0) {
    return (
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center gap-4 p-4 border-b border-white/10 bg-black/30 backdrop-blur-sm">
          {isMobile && (
            <Button
              variant="ghost"
              onClick={onBack}
              css="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <BackIcon className="w-5 h-5 text-white" />
            </Button>
          )}
          <div className="flex items-center gap-3 flex-1">
            <div className="w-10 h-10 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-lg">
              <Text className="text-white font-semibold text-sm">
                {getInitials(user.userName)}
              </Text>
            </div>
            <div className="flex flex-col">
              <Text className="font-semibold text-white">{user.userName}</Text>
              <Text className="text-sm text-gray-400">Loading messages...</Text>
            </div>
          </div>
        </div>

        {/* Loading State */}
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <Text className="text-gray-400">Loading conversation...</Text>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center gap-4 p-4 border-b border-white/10 bg-black/30 backdrop-blur-sm">
          {isMobile && (
            <Button
              variant="ghost"
              onClick={onBack}
              css="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <BackIcon className="w-5 h-5 text-white" />
            </Button>
          )}
          <div className="flex items-center gap-3 flex-1">
            <div className="w-10 h-10 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center">
              <Text className="text-white font-semibold text-sm">⚠️</Text>
            </div>
            <div className="flex flex-col">
              <Text className="font-semibold text-white">{user.userName}</Text>
              <Text className="text-sm text-red-400">Error loading messages</Text>
            </div>
          </div>
          <Button
            onClick={handleRefresh}
            variant="ghost"
            css="p-2 hover:bg-white/10 rounded-lg transition-colors"
          >
            <RefreshIcon className="w-5 h-5 text-orange-400" />
          </Button>
        </div>

        {/* Error State */}
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md mx-auto p-6">
            <Text className="text-red-400 mb-4">{error}</Text>
            <Button 
              onClick={handleRefresh}
              css="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center gap-4 p-4 border-b border-white/10 bg-black/30 backdrop-blur-sm">
        {isMobile && (
          <Button
            variant="ghost"
            onClick={onBack}
            css="p-2 hover:bg-white/10 rounded-lg transition-colors"
          >
            <BackIcon className="w-5 h-5 text-white" />
          </Button>
        )}
        <div className="flex items-center gap-3 flex-1">
          {user.userImage ? (
            <img
              src={user.userImage}
              alt={user.userName}
              className="w-10 h-10 rounded-full object-cover border-2 border-white/10 shadow-lg"
            />
          ) : (
            <div className="w-10 h-10 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-lg">
              <Text className="text-white font-semibold text-sm">
                {getInitials(user.userName)}
              </Text>
            </div>
          )}
          <div className="flex flex-col">
            <Text className="font-semibold text-white">{user.userName}</Text>
            <Text className="text-sm text-orange-300">{messages.length} messages</Text>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            onClick={openThreadFromHeader}
            variant="ghost"
            css="relative p-2 hover:bg-white/10 rounded-lg transition-colors"
            title="Open user thread"
          >
            <ThreadIcon className="w-5 h-5 text-orange-400" />
            {hasUnseenThread && (
              <span className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-orange-500 animate-pulse"></span>
            )}
          </Button>
          <Button
            onClick={() => setShowTagEditor(true)}
            variant="ghost"
            css="p-2 hover:bg-white/10 rounded-lg transition-colors flex-shrink-0"
            title="Edit user tags"
          >
            <TagIcon className="w-5 h-5 text-orange-400" />
          </Button>
          <Button
            onClick={handleSummaryButton}
            disabled={summaryLoading}
            variant="ghost"
            css={`p-2 ${summaryLoading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-white/10'} rounded-lg transition-colors`}
            title="Generate conversation summary"
          >
            {summaryLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500"></div>
            ) : (
              <ThreeSparklesIcon className="w-5 h-5 text-orange-400" />
            )}
          </Button>
          <Button
            onClick={handleRefresh}
            variant="ghost"
            css="p-2 hover:bg-white/10 rounded-lg transition-colors"
          >
            <RefreshIcon className="w-5 h-5 text-orange-400" />
          </Button>
          {!isMobile && (
            <Button
              onClick={onBack}
              variant="ghost"
              css="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <BackIcon className="w-5 h-5 text-white" />
            </Button>
          )}
        </div>
      </div>

      {/* Summary Overlay */}
      {showSummary && summary && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm p-4" onClick={() => setShowSummary(false)}>
          <div
            className="bg-black/90 border border-white/10 rounded-2xl shadow-2xl max-w-lg w-full p-6 relative overflow-y-auto max-h-[80vh]"
            onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside card
          >
            {/* Close button */}
            <button
              className="absolute top-3 right-3 p-2 rounded-md hover:bg-white/10 transition-colors"
              onClick={() => setShowSummary(false)}
              aria-label="Close summary"
            >
              <X className="w-4 h-4 text-gray-300" />
            </button>

            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <ThreeSparklesIcon className="w-5 h-5 text-orange-400" />
                <Text className="text-lg font-semibold text-white">Conversation Summary</Text>
              </div>

              <Text className="text-sm text-gray-200 whitespace-pre-wrap leading-relaxed">
                {summary}
              </Text>

              {summaryTokenInfo && (
                <div className="text-xs text-gray-400 border-t border-white/10 pt-3 flex justify-between">
                  <span>Input: {summaryTokenInfo.inputTokens}</span>
                  <span>Output: {summaryTokenInfo.outputTokens}</span>
                  <span>Total: {summaryTokenInfo.totalTokens}</span>
                  <span>{summaryTokenInfo.responseTime}s</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Tag Editor Overlay */}
      <TagEditor
        initialTags={user.tags || []}
        visible={showTagEditor}
        onClose={() => setShowTagEditor(false)}
        onSave={async (tags) => {
          setShowTagEditor(false);
          // Optimistic update
          if (onTagsUpdated) onTagsUpdated(user.channelId, tags);
          // Backend update
          try {
            await updateUserTags(coach.name, user.channelId, tags);
          } catch (err) {
            // Optionally: show error/toast, or refetch tags
            console.error('Failed to update tags', err);
          }
        }}
      />

      {/* Messages */}
      <div className="flex-1 overflow-hidden bg-black/5">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-sm mx-auto px-6">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-400/20 to-gray-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Text className="text-2xl">💬</Text>
              </div>
              <div className="flex flex-col items-center gap-1">
                <Text className="text-lg text-white font-semibold">No messages yet</Text>
                <Text className="text-sm text-gray-400 leading-relaxed">
                  The conversation will appear here
                </Text>
              </div>
            </div>
          </div>
        ) : (
          <ScrollArea className="h-full" ref={scrollAreaRef} onScroll={handleScroll}>
            {/* Improved loading more indicator */}
            {loadingMore && (
              <div className="sticky top-0 z-10 bg-gradient-to-b from-black/80 to-transparent backdrop-blur-sm border-b border-white/10">
                <div className="flex items-center justify-center py-3">
                  <div className="flex items-center gap-3 bg-black/60 backdrop-blur-sm px-4 py-2 rounded-full border border-white/20">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500"></div>
                    <Text className="text-sm text-gray-300 font-medium">Loading older messages...</Text>
                  </div>
                </div>
              </div>
            )}

            <div className="p-4 space-y-4 w-full" style={{ paddingBottom: isMobile ? '7rem' : undefined }}>
              {messages.map((message, index) => {
                // Special rendering for summary messages
                if (message.messageType === 'summary') {
                  const isOpen = openSummaryIds.has(message.id);
                  return (
                    <div key={message.id} data-message-id={message.id} className="w-full flex flex-col items-center">
                      {/* Timestamp (optional) */}
                      <div className="flex justify-center mb-2">
                        <div className="bg-black/20 backdrop-blur-sm px-3 py-1 rounded-full">
                          <Text className="text-xs text-gray-400">
                            {formatMessageDate(message.date)}
                          </Text>
                        </div>
                      </div>

                      <button
                        onClick={() => toggleSummaryVisibility(message.id)}
                        className="text-sm text-orange-300 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg px-4 py-2 focus:outline-none"
                      >
                        {isOpen ? 'Hide Summary' : 'Generated Summary'}
                      </button>

                      {isOpen && (
                        <div className="mt-3 bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-4 max-w-[70%]">
                          <Text className="text-sm text-white whitespace-pre-wrap leading-relaxed">
                            {message.content}
                          </Text>
                        </div>
                      )}
                    </div>
                  );
                }

                // Special rendering for coach messages sent by coach
                // Thread pill for coach_message sent by USER
                if (message.messageType === 'coach_message' && message.sender === 'user') {
                  // Collapse consecutive user thread markers
                  const prev = index > 0 ? messages[index - 1] : null;
                  if (prev && prev.messageType === 'coach_message' && prev.sender === 'user') {
                    return null;
                  }

                  const unseen = !message.status || message.status === 'Unseen';

                  return (
                    <div key={message.id} className="flex justify-center mb-6 w-full">
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-[#FF5727]/30 via-[#FCA311]/40 to-[#FF5727]/30 rounded-2xl blur-sm"></div>
                        <Button
                          variant="ghost"
                          css="relative bg-gradient-to-r from-[#1a1a1a] to-[#2a2a2a] border border-[#FCA311]/50 hover:border-[#FCA311] text-[#FCA311] hover:text-white px-6 py-3 rounded-2xl backdrop-blur-sm shadow-lg hover:shadow-[0_8px_32px_rgba(252,163,17,0.25)] transition-all duration-300 ease-out transform hover:scale-[1.02] flex items-center gap-3 text-sm font-medium"
                          onClick={() => openUserThread(message)}
                        >
                          <div className="w-5 h-5 bg-gradient-to-br from-[#FCA311] to-[#FF5727] rounded-full flex items-center justify-center flex-shrink-0">
                            <ThreadIcon className="w-3.5 h-3.5 text-black/80" />
                          </div>
                          <span className="whitespace-nowrap">User thread</span>
                          {unseen && (
                            <span className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-orange-500 animate-pulse"></span>
                          )}
                        </Button>
                      </div>
                    </div>
                  );
                }

                // Skip direct messages sent by coach; they should only appear inside the thread overlay
                if (message.messageType === 'coach_message' && message.sender === 'coach') {
                  return null;
                }

                // Default rendering for regular messages
                const isUser = message.sender === 'user';
                const showTimestamp = index === 0 || 
                  (new Date(message.date).getTime() - new Date(messages[index - 1].date).getTime()) > 300000; // 5 minutes

                return (
                  <div key={message.id} data-message-id={message.id} className="w-full">
                    {showTimestamp && (
                      <div className="flex justify-center mb-4">
                        <div className="bg-black/20 backdrop-blur-sm px-3 py-1 rounded-full">
                          <Text className="text-xs text-gray-400">
                            {formatMessageDate(message.date)}
                          </Text>
                        </div>
                      </div>
                    )}
                    
                    {/* Message bubble */}
                    <div className={`flex w-full ${isUser ? 'justify-end' : 'justify-start'}`}>
                      <div className={`relative z-10 max-w-[70%] px-4 py-3 rounded-2xl shadow-lg ${
                        isUser 
                          ? 'bg-gradient-to-br from-orange-700 to-orange-800 text-white ml-auto' 
                          : 'bg-white/10 backdrop-blur-sm text-white border border-white/10 mr-auto'
                      }`}>
                        <Text className="text-sm break-words whitespace-pre-wrap leading-relaxed mb-3">
                          {message.content}
                        </Text>
                        
                        {/* Audio indicator */}
                        {message.audio && (
                          <div className="flex items-center gap-2 text-xs opacity-75 mb-2">
                            <span>🎵</span>
                            <span>Audio message</span>
                          </div>
                        )}
                        
                        <Text className={`text-xs block ${
                          isUser ? 'text-orange-100' : 'text-gray-400'
                        }`}>
                          {new Date(message.date).toLocaleTimeString([], { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </Text>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>


          </ScrollArea>
        )}
      </div>

      {/* Send direct message */}
      <div
          ref={inputContainerRef}
          className={`border-t border-white/10 bg-black/30 p-4 flex items-center gap-3 ${isMobile ? 'fixed bottom-0 left-0 right-0 z-20 backdrop-blur-lg' : ''}`}
        >
        <input
          type="text"
          value={newMessage}
          onFocus={handleInputFocus}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="Send a direct message to this user..."
          className="flex-1 bg-white/10 text-white placeholder-gray-400 border border-white/20 rounded-lg px-4 py-2 focus:outline-none focus:border-orange-500 focus:ring-1 focus:ring-orange-500"
        />
        <Button
          onClick={() => handleSendMessage(newMessage)}
          disabled={sending || newMessage.trim() === ""}
          css={`${sending ? 'bg-white/10 text-gray-400 cursor-not-allowed' : 'bg-orange-600 hover:bg-orange-700 text-white'} px-4 py-2 rounded-lg whitespace-nowrap`}
        >
          {sending ? 'Sending...' : 'Send'}
        </Button>
      </div>

      <UserThread
        user={user}
        visible={showUserThread}
        onClose={() => setShowUserThread(false)}
        messages={userThreadMessages}
        initialMessageId={threadAnchorId}
        coachName={coach.name}
        channelId={user.channelId}
      />
    </div>
  );
}; 