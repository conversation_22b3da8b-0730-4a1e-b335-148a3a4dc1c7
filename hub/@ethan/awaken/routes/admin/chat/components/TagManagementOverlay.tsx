"use client";

import { useState, useEffect } from "npm:react@canary";
import { Button, Text, Input, X } from "@reframe/ui/main.tsx";
import { getUserCoachTags, updateUserCoachTags } from "../../actions/admin-actions.ts";

interface TagManagementOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  user: {
    channelId: number;
    userName: string;
    userEmail: string;
  };
  coach: {
    name: string;
    email: string;
  };
  onTagsUpdated: (channelId: number, newTags: string[]) => void;
}

export const TagManagementOverlay: React.FC<TagManagementOverlayProps> = ({
  isOpen,
  onClose,
  user,
  coach,
  onTagsUpdated,
}) => {
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load existing tags when overlay opens
  useEffect(() => {
    if (isOpen && user.channelId && coach.name) {
      loadTags();
    }
  }, [isOpen, user.channelId, coach.name]);

  const loadTags = async () => {
    try {
      setLoading(true);
      setError(null);
      const existingTags = await getUserCoachTags(user.channelId, coach.name);
      setTags(existingTags);
    } catch (err) {
      console.error("Error loading tags:", err);
      setError("Failed to load existing tags");
    } finally {
      setLoading(false);
    }
  };

  const handleAddTag = () => {
    const trimmedTag = newTag.trim();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags(prev => [...prev, trimmedTag]);
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      
      const success = await updateUserCoachTags(user.channelId, coach.name, tags);
      
      if (success) {
        // Notify parent component about the update
        onTagsUpdated(user.channelId, tags);
        onClose();
      } else {
        setError("Failed to update tags");
      }
    } catch (err) {
      console.error("Error saving tags:", err);
      setError("Failed to save tags");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setNewTag("");
    setError(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg border border-white/10 shadow-xl max-w-md w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <div>
            <Text className="text-lg font-semibold text-white">
              Manage Tags
            </Text>
            <Text className="text-sm text-gray-400 mt-1">
              {user.userName} • {coach.name}
            </Text>
          </div>
          <Button
            onClick={handleCancel}
            css="p-2 hover:bg-white/10 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4 max-h-[60vh] overflow-y-auto">
          {/* Add new tag */}
          <div className="space-y-2">
            <Text className="text-sm font-medium text-white">Add New Tag</Text>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Enter tag name..."
                className="flex-1 bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                disabled={saving}
              />
              <Button
                onClick={handleAddTag}
                disabled={!newTag.trim() || saving}
                css="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add
              </Button>
            </div>
          </div>

          {/* Current tags */}
          <div className="space-y-2">
            <Text className="text-sm font-medium text-white">Current Tags</Text>
            {loading ? (
              <Text className="text-gray-400 text-sm">Loading tags...</Text>
            ) : tags.length === 0 ? (
              <Text className="text-gray-400 text-sm">No tags assigned</Text>
            ) : (
              <div className="flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-2 px-3 py-1 bg-orange-500/20 text-orange-300 rounded-full border border-orange-500/30"
                  >
                    <Text className="text-sm">{tag}</Text>
                    <button
                      onClick={() => handleRemoveTag(tag)}
                      disabled={saving}
                      className="text-orange-300 hover:text-orange-100 disabled:opacity-50"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Error message */}
          {error && (
            <div className="p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
              <Text className="text-red-300 text-sm">{error}</Text>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-white/10">
          <Button
            onClick={handleCancel}
            disabled={saving}
            css="px-4 py-2 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg disabled:opacity-50"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving}
            css="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg disabled:opacity-50"
          >
            {saving ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>
    </div>
  );
};
