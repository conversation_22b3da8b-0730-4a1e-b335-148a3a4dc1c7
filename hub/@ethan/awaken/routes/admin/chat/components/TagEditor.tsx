"use client";

import { useState, useEffect } from "npm:react@canary";
import { Button, Text, Input, X } from "@reframe/ui/main.tsx";

interface TagEditorProps {
  initialTags: string[];
  visible: boolean;
  onClose: () => void;
  onSave: (tags: string[]) => void;
}

export const TagEditor: React.FC<TagEditorProps> = ({ initialTags, visible, onClose, onSave }) => {
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");

  useEffect(() => {
    setTags(initialTags);
  }, [initialTags, visible]);

  if (!visible) return null;

  const addTag = () => {
    const t = newTag.trim();
    if (t && !tags.includes(t)) {
      setTags([...tags, t]);
    }
    setNewTag("");
  };

  const removeTag = (t: string) => {
    setTags(tags.filter(tag => tag !== t));
  };

  const handleSubmit = () => {
    onSave(tags);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-[#1a1a1a] border border-white/10 rounded-lg w-full max-w-md p-6 mx-4 relative">
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-400 hover:text-white"
        >
          <X className="w-5 h-5" />
        </button>
        <Text className="text-xl font-semibold text-white mb-4">Edit User Tags</Text>

        {/* Tag list */}
        <div className="flex flex-wrap gap-2 mb-4">
          {tags.map(tag => (
            <span
              key={tag}
              className="flex items-center gap-1 bg-white/10 text-gray-200 px-2 py-1 text-xs rounded-full"
            >
              {tag}
              <button
                onClick={() => removeTag(tag)}
                className="hover:text-orange-400"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          ))}
        </div>

        {/* Add new tag */}
        <div className="flex gap-2 mb-6">
          <Input
            value={newTag}
            placeholder="New tag"
            onChange={(e) => setNewTag(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") addTag();
            }}
            css="flex-1 bg-white/10 border-white/20 text-white placeholder-gray-400 rounded-lg px-3 py-2"
          />
          <Button onClick={addTag} css="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg">
            Add
          </Button>
        </div>

        <div className="flex justify-end gap-2">
          <Button
            variant="ghost"
            onClick={onClose}
            css="px-4 py-2 rounded-lg hover:bg-white/10 text-gray-300"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            css="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg"
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
};
