"use client";

import { useEffect, useState, useRef } from "npm:react@canary";
import { Button, ScrollArea, Text, Input } from "@reframe/ui/main.tsx";
import { UserList } from "./components/UserList.tsx";
import { ChatView } from "./components/ChatView.tsx";
import { getCoachUsersWithLastMessage, getCoachByEmail } from "../../admin/actions/admin-actions.ts";
import { BackIcon, UsersIcon } from "../../../lib/icons.tsx";
import { Layout } from "../../../lib/layout.tsx";

interface User {
  channelId: number;
  userName: string;
  userEmail: string;
  userImage?: string | null;
  tags?: string[];
  hasUnseen: {
    regularMessage: boolean;
    userThreadMessage: boolean;
  };

  lastMessage: {
    content: string;
    date: string;
    sender: string;
    type: string;
  };
}

interface Coach {
  id: number | string;
  name: string;
  email: string;
  description?: string | null;
  type?: string | null;
  metadata?: string | null;
}

export interface AdminChatProps {
  user: any;
}

// In-memory cache that persists for the session (no time expiration)
const coachDataCache = new Map<string, { coach: Coach, users: User[] }>();

export const AdminChat: React.FC<AdminChatProps> = ({ user }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [coach, setCoach] = useState<Coach | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  
  // Resizable column state
  const [sidebarWidth, setSidebarWidth] = useState(400); // Default width in pixels
  const [isResizing, setIsResizing] = useState(false);
  const resizeRef = useRef<HTMLDivElement>(null);

  const MIN_SIDEBAR_WIDTH = 300;
  const MAX_SIDEBAR_WIDTH = 600;

  // Handle resize functionality
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      
      const newWidth = e.clientX;
      if (newWidth >= MIN_SIDEBAR_WIDTH && newWidth <= MAX_SIDEBAR_WIDTH) {
        setSidebarWidth(newWidth);
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // Fetch coach details and users on mount
  const fetchCoachData = async (useCache = true) => {
    try {
      setLoading(true);
      setError(null);

      const cacheKey = user.email;

      // Check in-memory cache first
      if (useCache) {
        const cached = coachDataCache.get(cacheKey);
        if (cached) {
          setCoach(cached.coach);
          setUsers(cached.users);
          setLoading(false);
          return;
        }
      }

      // Get coach details by email
      const coachData = await getCoachByEmail(user.email);
      if (!coachData) {
        setError("Coach not found. Make sure your email is registered as a coach.");
        return;
      }

      setCoach({
        ...coachData,
        email: coachData.email || ""
      } as Coach);

      // Get users who have messaged this coach
      const usersData = await getCoachUsersWithLastMessage(user.email);

      // Sort users by priority:
      // 1. Conversations with unseen thread messages
      // 2. Conversations with unseen regular messages
      // 3. Conversations whose last message is a coach_message
      // 4. All remaining conversations
      // Within each bucket, sort by newest lastMessage.date first
      const sortUsers = (list: User[]) => {
        return [...list].sort((a, b) => {
          const priority = (u: User) => {
            if (u.hasUnseen.userThreadMessage) return 1;
            if (u.hasUnseen.regularMessage) return 2;
            if (u.lastMessage?.type === "coach_message" && u.lastMessage?.sender === "user") return 3;
            return 4;
          };

          const prioA = priority(a);
          const prioB = priority(b);

          if (prioA !== prioB) {
            return prioA - prioB; // lower number = higher priority
          }

          // Same priority group, tie-break by most recent date
          const dateA = new Date(a.lastMessage?.date).getTime();
          const dateB = new Date(b.lastMessage?.date).getTime();
          return dateB - dateA;
        });
      };

      const sortedUsers = sortUsers(usersData.users);

      setUsers(sortedUsers);

      // Cache the results (no expiration, persists for session)
      coachDataCache.set(cacheKey, {
        coach: coachData,
        users: sortedUsers
      });

    } catch (err: any) {
      console.error("Error fetching coach data:", err);
      setError(err.message || "Failed to load coach data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.email) {
      fetchCoachData();
    }
  }, [user?.email]);

  // Filter users based on search query
  const filteredUsers = users.filter(user => 
    user.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.userEmail?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.lastMessage.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Decide whether selecting a conversation should clear the unseen indicator
  const handleUserSelect = (user: User) => {
    // If the last unseen message is a regular conversation message we clear the
    // indicator immediately. If it’s an unseen thread message we keep it until
    // the coach explicitly opens the thread overlay.
    const shouldClearImmediately =
      user.hasUnseen.regularMessage;

    if (shouldClearImmediately) {
      setUsers(prev => prev.map(u => u.channelId === user.channelId ? { ...u, hasUnseen: { regularMessage: false, userThreadMessage: false } } : u));
      setSelectedUser({ ...user, hasUnseen: { regularMessage: false, userThreadMessage: false } });
    } else {
      setSelectedUser(user);
    }
  };

  // Handle tag updates from ChatView
  const handleTagsUpdated = (channelId: number, newTags: string[]) => {
    // Update the user in the users list
    setUsers(prev => prev.map(u =>
      u.channelId === channelId ? { ...u, tags: newTags } : u
    ));

    // Update the selected user if it's the same one
    if (selectedUser && selectedUser.channelId === channelId) {
      setSelectedUser(prev => prev ? { ...prev, tags: newTags } : null);
    }
  };

  // Callback fired from ChatView when the coach opens the user thread overlay
  const handleThreadOpened = (channelId: number | string) => {
    setUsers(prev => prev.map(u => u.channelId === channelId ? { ...u, hasUnseen: { regularMessage: false, userThreadMessage: false } } : u));

    // Also update selectedUser state so the pill disappears in header after thread view
    setSelectedUser(prev => prev && prev.channelId === channelId ? { ...prev, hasUnseen: { regularMessage: false, userThreadMessage: false } } : prev);
  };

  const handleBackToList = () => {
    setSelectedUser(null);
  };

  const handleRefresh = () => {
    // Clear cache and refetch
    const cacheKey = user.email;
    coachDataCache.delete(cacheKey);
    fetchCoachData(false);
  };

  if (loading) {
    return (
      <Layout user={user} userData={null} currentPath="admin-chat" keepDrawer={false}>
        <div className="flex items-center justify-center h-full min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <Text className="text-gray-400">Loading coach dashboard...</Text>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout user={user} userData={null} currentPath="admin-chat" keepDrawer={false}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="text-red-500 text-5xl mb-4">⚠️</div>
            <Text className="text-red-400 text-lg mb-4">{error}</Text>
            <Button 
              onClick={handleRefresh} 
              css="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Try Again
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout user={user} userData={null} currentPath="admin-chat" keepDrawer={false}>
      <div className="flex h-full text-white overflow-hidden min-h-screen">
        {/* Desktop Layout */}
        <div className="hidden md:flex w-full relative">
          {/* Left Sidebar - User List with resizable width */}
          <div 
            className="border-r border-white/10 flex flex-col bg-black/20 backdrop-blur-sm relative"
            style={{ width: `${sidebarWidth}px`, minWidth: `${MIN_SIDEBAR_WIDTH}px`, maxWidth: `${MAX_SIDEBAR_WIDTH}px` }}
          >
            {/* Header */}
            <div className="p-6 border-b border-white/10 bg-black/30">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-lg flex-shrink-0">
                    <Text className="font-bold text-white text-lg">{coach?.name?.[0] || 'C'}</Text>
                  </div>
                  <div className="flex flex-col min-w-0 flex-1">
                    <Text className="font-semibold text-white text-lg truncate">{coach?.name || 'Coach'}</Text>
                    <Text className="text-sm text-orange-300 whitespace-nowrap">{users.length} conversations</Text>
                  </div>
                </div>
                <Button
                  onClick={handleRefresh}
                  variant="ghost"
                  css="p-2 hover:bg-white/10 rounded-lg transition-colors flex-shrink-0"
                  title="Refresh conversations"
                >
                  <UsersIcon className="w-5 h-5 text-orange-400" />
                </Button>
              </div>
              
              {/* Search Bar */}
              <Input
                type="text"
                placeholder="Search users or messages..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                css="w-full bg-white/10 border-white/20 text-white placeholder-gray-400 focus:border-orange-500 focus:ring-orange-500/20 rounded-lg px-4 py-2 transition-all"
              />
            </div>

            {/* User List */}
            <div className="flex-1 overflow-hidden">
              <UserList 
                users={filteredUsers}
                onUserSelect={handleUserSelect}
              />
            </div>

            {/* Resize Handle */}
            <div
              ref={resizeRef}
              className={`absolute top-0 right-0 w-1 h-full cursor-col-resize hover:bg-orange-500/50 transition-colors group ${
                isResizing ? 'bg-orange-500' : 'bg-transparent'
              }`}
              onMouseDown={handleMouseDown}
            >
              <div className="absolute top-1/2 -translate-y-1/2 -right-1 w-3 h-8 bg-white/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <div className="w-0.5 h-4 bg-white/60 rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Right Side - Chat View */}
          <div className="flex-1 flex flex-col bg-black/10 min-w-0">
            {selectedUser && coach ? (
              <ChatView
                user={selectedUser}
                coach={coach}
                onBack={handleBackToList}
                onThreadOpened={handleThreadOpened}
                onTagsUpdated={handleTagsUpdated}
              />
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-orange-400/20 to-orange-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <UsersIcon className="w-10 h-10 text-orange-400" />
                  </div>
                  <div className="flex flex-col items-center gap-1">
                    <Text className="text-xl text-white font-semibold">Select a conversation</Text>
                    <Text className="text-sm text-gray-400">
                      Choose a user from the list to view their messages
                    </Text>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="md:hidden w-full min-h-[100dvh] max-h-[100dvh]">
          {selectedUser && coach ? (
            <ChatView 
              user={selectedUser}
              coach={coach}
              onBack={handleBackToList}
              isMobile={true}
              onThreadOpened={handleThreadOpened}
            />
          ) : (
            <div className="flex flex-col h-full">
              {/* Mobile Header */}
              <div className="p-4 border-b border-white/10 bg-black/30 backdrop-blur-sm">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3 min-w-0 flex-1">
                    <div className="w-10 h-10 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <Text className="font-bold text-white">{coach?.name?.[0] || 'C'}</Text>
                    </div>
                    <div className="flex flex-col min-w-0 flex-1">
                      <Text className="font-semibold text-white truncate">{coach?.name || 'Coach'}</Text>
                      <Text className="text-sm text-orange-300 whitespace-nowrap">{users.length} conversations</Text>
                    </div>
                  </div>
                  <Button
                    onClick={handleRefresh}
                    variant="ghost"
                    css="p-2 hover:bg-white/10 rounded-lg transition-colors flex-shrink-0"
                    title="Refresh conversations"
                  >
                    <UsersIcon className="w-5 h-5 text-orange-400" />
                  </Button>
                </div>
                
                {/* Search Bar */}
                <Input
                  type="text"
                  placeholder="Search users or messages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  css="w-full bg-white/10 border-white/20 text-white placeholder-gray-400 focus:border-orange-500 rounded-lg px-4 py-2"
                />
              </div>

              {/* Mobile User List */}
              <div className="flex-1 overflow-hidden">
                <UserList 
                  users={filteredUsers}
                  onUserSelect={handleUserSelect}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}; 