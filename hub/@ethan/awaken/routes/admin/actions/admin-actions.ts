"use server";

import { db } from "../../../lib/db.ts";
import Reframe from "@";
import CryptoJS from "npm:crypto-js";
import { SUPERUSER_EMAILS, getAnalysisPrompt } from "../../../lib/server-constants.ts";
import { getSession } from "../../../lib/get-session.ts";
import { sql } from "npm:kysely";
import { saveMessage } from "../../../actions/db/conversation-actions.ts";
import { getCoachPrompt } from "../../../action.ts";
import { getUserInfo } from "../../../lib/get-user-info.ts";

// Types for analysis prompt modes
type AnalysisPromptMode = 
  | 'kokoro_meta'        // Admin analysis - uses getAnalysisPrompt
  | 'coach_summary';     // Coach-specific summary - uses coachSummaryPrompt as system

interface AnalysisOptions {
  mode: 'all' | 'single';
  model: 'gemini-2.5-pro-preview-06-05' | 'openai-openrouter' | 'openai-api-4.5' | 'openai-api-o1-medium' | 'openai-api-o1-high';
  promptMode: AnalysisPromptMode;
  coachName?: string; // Required when promptMode is 'coach_summary'
}

/**
 * Default summary prompt for when no coach-specific prompt is found
 */
const getDefaultSummaryPrompt = (): string => {
  return `Based on the conversation between the client and the coach, please provide a concise summary that includes:

* **Topics Discussed:**
* **People Involved (Names & Relationships):**
* **Breakthroughs:**
* **Proposed/Committed Actions:**
* **Client's Current State:**`;
};

/**
 * Efficiently fetches the most recent X messages for each channel with proper limits
 * 
 * @param channelIds - Array of channel IDs to fetch messages for
 * @param limit - Maximum number of messages to fetch per channel
 * @returns Messages grouped by channel ID
 */
async function getRecentMessagesForChannels(channelIds: number[], limit: number = 10) {
  if (channelIds.length === 0) return {};
  
  try {
    console.log(`[ADMIN] Fetching ${limit} recent messages for ${channelIds.length} channels`);
    
    // Initialize storage for messages by channel
    const groupedMessages: Record<string, any[]> = {};
    let totalMessagesRetrieved = 0;
    
    // Fetch messages for each channel separately with proper limits
    // This is more efficient than fetching all and filtering in memory
    for (const channelId of channelIds) {
      const messages = await db
        .selectFrom("conversation")
        .selectAll()
        .where("channelId", "=", channelId)
        .where("messageType", "=", "message")
        .orderBy("date", "desc")
        .limit(limit)
        .execute();
      
      totalMessagesRetrieved += messages.length;
      
      if (messages.length > 0) {
        // Reverse to get chronological order (oldest first)
        groupedMessages[channelId.toString()] = messages.reverse();
      }
    }
    
    console.log(`[ADMIN] Retrieved ${totalMessagesRetrieved} total messages (max ${limit} per channel)`);
    
    return groupedMessages;
  } catch (error) {
    console.error("[ADMIN] Error fetching recent messages:", error);
    throw error;
  }
}

/**
 * Fetches the most recent X messages overall and groups them by channel
 * This provides a different view compared to fetching per-channel messages
 * 
 * @param totalMessages - Total number of most recent messages to fetch
 * @returns Messages grouped by channel ID
 */
async function getLatestMessagesAndGroupByChannel(totalMessages: number = 100) {
  try {
    console.log(`[ADMIN] Fetching ${totalMessages} most recent messages overall`);
    
    // Fetch the most recent messages across all channels
    const messages = await db
      .selectFrom("conversation")
      .selectAll()
      .where("messageType", "=", "message")
      .orderBy("date", "desc")
      .limit(totalMessages)
      .execute();
    
    console.log(`[ADMIN] Retrieved ${messages.length} total messages`);
    
    // Group messages by channel
    const groupedMessages: Record<string, any[]> = {};
    
    // Process in reverse to maintain chronological order within each channel
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      const channelId = message.channelId.toString();
      
      if (!groupedMessages[channelId]) {
        groupedMessages[channelId] = [];
      }
      
      groupedMessages[channelId].push(message);
    }
    
    console.log(`[ADMIN] Grouped into ${Object.keys(groupedMessages).length} channels`);
    
    return groupedMessages;
  } catch (error) {
    console.error("[ADMIN] Error fetching latest messages:", error);
    throw error;
  }
}

/**
 * Fetches all conversations grouped by channel ID
 * Admin-only function to retrieve chat history for analysis
 * @param fetchMode - Method to use for fetching conversations ('byChannel' or 'recentOverall')
 * @param messagesLimit - Number of messages to fetch (per channel or total, depending on mode)
 */
export const getAllChannelConversations = async (
  fetchMode: 'byChannel' | 'recentOverall' = 'byChannel',
  messagesLimit: number = fetchMode === 'byChannel' ? 40 : 200
) => {
  // Verify the user is an admin
  const user = await getSession();
  if (!user || !SUPERUSER_EMAILS.includes(user.email)) {
    throw new Error("Unauthorized: Admin access required");
  }
  
  try {
    console.log(`[ADMIN] Fetching all channel conversations using mode: ${fetchMode}`);
    
    let groupedMessagesWithEncryption: Record<string, any[]> = {};
    
    if (fetchMode === 'byChannel') {
      // First, get a list of all unique channel IDs
      const channelResults = await db
        .selectFrom("user")
        .select(["channelId"])
        .distinct()
        .execute();
      
      const channelIds = channelResults.map(row => row.channelId);
      console.log(`[ADMIN] Found ${channelIds.length} unique channels`);
      
      // Use our optimized query to get messages for all channels at once
      groupedMessagesWithEncryption = await getRecentMessagesForChannels(channelIds, messagesLimit);
    } else {
      // Fetch the most recent messages overall and group by channel
      groupedMessagesWithEncryption = await getLatestMessagesAndGroupByChannel(messagesLimit);
    }
    
    // Get the encryption key
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Encryption key not found");
    }
    
    // Process all channels and decrypt messages
    const groupedConversations: Record<string, any[]> = {};
    let totalMessages = 0;
    
    for (const channelId in groupedMessagesWithEncryption) {
      const messages = groupedMessagesWithEncryption[channelId];
      const decryptedMessages: any[] = [];
      
      // Decrypt messages for this channel
      for (const message of messages) {
        try {
          // Using the same decryption approach as in conversation-actions.ts
          let decryptedContent;
          try {
            // First try to decrypt the message
            decryptedContent = CryptoJS.AES.decrypt(message.content, secretKey).toString(CryptoJS.enc.Utf8);
            
            // If decrypted content is empty but the original content isn't,
            // it might be an unencrypted legacy message
            if (!decryptedContent && message.content) {
              console.log(`[ADMIN] Message ${message.id} appears to be unencrypted, using original content`);
              decryptedContent = message.content;
            }
          } catch (decryptError) {
            // Skip messages that can't be decrypted and log error
            console.error(`[ADMIN] Failed to decrypt message ${message.id}:`, (decryptError as Error).message);
            continue;
          }
          
          // Skip messages with empty content after decryption attempts
          if (!decryptedContent) {
            console.log(`[ADMIN] Skipping message ${message.id} with empty content`);
            continue;
          }
          
          decryptedMessages.push({
            ...message,
            content: decryptedContent
          });
        } catch (error) {
          console.error(`[ADMIN] Error processing message ${message.id}:`, (error as Error).message);
        }
      }
      
      // Only add channels that have messages after decryption
      if (decryptedMessages.length > 0) {
        groupedConversations[channelId] = decryptedMessages;
        totalMessages += decryptedMessages.length;
      }
    }
    
    // Count channels and total messages
    const channelCount = Object.keys(groupedConversations).length;
    
    console.log(`[ADMIN] Successfully processed ${totalMessages} messages from ${channelCount} channels`);
    
    return { 
      conversations: groupedConversations,
      stats: {
        channelCount,
        totalMessages,
        fetchMode
      }
    };
  } catch (error) {
    console.error("[ADMIN] Error fetching all channel conversations:", error);
    throw error;
  }
};

/**
 * Analyzes conversations using AI
 * Takes in conversations (grouped by channel) and a query/prompt
 */
export const analyzeConversations = async (
  conversations: Record<string, any[]>,
  query: string,
  options: AnalysisOptions
) => {
  // Verify the user is an admin
  const user = await getSession();
  if (!user || !SUPERUSER_EMAILS.includes(user.email)) {
    throw new Error("Unauthorized: Admin access required");
  }
  
  try {
    console.log("[ADMIN] Analyzing conversations with query:", query);
    console.log("[ADMIN] Analysis mode:", options.mode);
    console.log("[ADMIN] Using model:", options.model);
    console.log("[ADMIN] Prompt mode:", options.promptMode);
    
    // Count total messages and channels
    const channelCount = Object.keys(conversations).length;
    let totalMessages = 0;
    
    Object.values(conversations).forEach(channelMessages => {
      totalMessages += channelMessages.length;
    });
    
    console.log(`[ADMIN] Analyzing ${totalMessages} messages from ${channelCount} channels`);
    
    // For single user analysis, we can include more messages
    // For coach summaries, use all messages since we already filtered to 2 weeks
    const messagesPerChannel = options.promptMode === 'coach_summary' 
      ? Number.MAX_SAFE_INTEGER  // Use all messages for summaries
      : (options.mode === 'single' ? 100 : 40); // Keep limits for analysis
    
    console.log(`[ADMIN] Message limit per channel: ${messagesPerChannel === Number.MAX_SAFE_INTEGER ? 'unlimited (coach summary)' : messagesPerChannel}`);
    
    // Prepare conversations in a format suitable for AI
    const conversationSummaries = Object.entries(conversations).map(([channelId, messages]) => {
      // Limit messages per channel for analysis, but use all for summaries
      const limitedMessages = messages.slice(-messagesPerChannel);
      
      console.log(`[ADMIN] Channel ${channelId}: Using ${limitedMessages.length}/${messages.length} messages`);
      
      return `
CHANNEL_ID: ${channelId}
MESSAGE_COUNT: ${messages.length}
MESSAGES:
${limitedMessages.map(msg => 
  `[${new Date(msg.date).toISOString()}] ${msg.sender}: ${msg.content}`
).join('\n')}
      `;
    }).join('\n\n====================\n\n');
    
    // Resolve system instruction and user prompt based on prompt mode
    let systemInstruction: string;
    let userPrompt: string;

    switch (options.promptMode) {
      case 'coach_summary':
        if (!options.coachName) {
          throw new Error("coachName is required when promptMode is 'coach_summary'");
        }
        
        console.log(`[ADMIN] Using coach-specific summary prompt for coach: ${options.coachName}`);
        const { SystemPrompt: coachPrompt } = await getCoachPrompt(options.coachName, "coachSummaryPrompt");
        
        // Use coach-specific prompt or fallback to default summary prompt
        systemInstruction = coachPrompt || getDefaultSummaryPrompt();
        userPrompt = conversationSummaries; // The formatted conversation data
        
        console.log(`[ADMIN] Coach prompt found: ${!!coachPrompt}`);
        break;
        
      case 'kokoro_meta':
      default:
        console.log("[ADMIN] Using Kokoro Meta analysis prompt");
        systemInstruction = getAnalysisPrompt(options.mode, totalMessages, conversationSummaries, channelCount);
        userPrompt = query; // The user's analysis question
        break;
    }
      
    // Call the appropriate model API based on the selected model
    let result;
    if (options.model === 'openai-openrouter') {
      // Use OpenRouter with OpenAI model
      result = await callOpenRouterAPI("openai/gpt-4.5-preview", systemInstruction, userPrompt);
    } else if (options.model === 'openai-api-4.5') {
      // Use OpenAI API directly with GPT-4.5
      result = await callOpenAIDirectAPI("gpt-4.5-preview-2025-02-27", systemInstruction, userPrompt);
    } else if (options.model === 'openai-api-o1-medium') {
      // Use OpenAI API directly with o1 model (medium reasoning)
      result = await callOpenAIDirectAPI("o1-2024-12-17", systemInstruction, userPrompt, "medium");
    } else if (options.model === 'openai-api-o1-high') {
      // Use OpenAI API directly with o1 model (high reasoning)
      result = await callOpenAIDirectAPI("o1-2024-12-17", systemInstruction, userPrompt, "high");
    } else if (options.model === 'gemini-2.5-pro-preview-06-05') {
      // Use OpenRouter with Gemini 2.5 Pro model
      result = await callOpenRouterAPI("google/gemini-2.5-pro-preview-06-05", systemInstruction, userPrompt);
    } else {
      // Fallback case: Log an error or default to a known model
      // For safety, let's default to the new Gemini model if the type somehow allows an unexpected value
      console.warn(`[ADMIN] Unexpected model value: ${options.model}. Defaulting to gemini-2.5-pro-preview-06-05.`);
      result = await callOpenRouterAPI("google/gemini-2.5-pro-preview-06-05", systemInstruction, userPrompt);
    }
    
    return result;
  } catch (error) {
    console.error("[ADMIN] Error analyzing conversations:", error);
    throw error;
  }
};

/**
 * Call OpenRouter API with specified model for conversation analysis
 */
async function callOpenRouterAPI(modelId: string, systemInstruction: string, userPrompt: string) {
  try {
    console.log(`[ADMIN] Calling OpenRouter API with model: ${modelId}`);
    
    // Check if this is a follow-up question
    const isFollowUp = userPrompt.includes("This is a follow-up question to our previous conversation");
    
    if (isFollowUp) {
      console.log("[ADMIN] Processing as follow-up question with conversation history");
      
      // Log the full prompt for debugging
      console.log("[ADMIN] Full follow-up prompt:", userPrompt);
    }
    
    // OpenRouter API configuration
    const API_KEY = "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";
    const openRouterUrl = "https://openrouter.ai/api/v1/chat/completions";
    
    // For follow-up questions, we need to properly extract the actual question from the context
    let messages: Array<{role: string; content: string}> = [];
    
    if (isFollowUp) {
      try {
        // Extract the actual question (first line before the context)
        const splitPrompt = userPrompt.split('\n\nThis is a follow-up question');
        const actualQuestion = splitPrompt[0].trim();
        
        // Parse out the conversation history
        const historyPart = userPrompt.split("For context, here's the history:")[1];
        if (!historyPart) {
          console.error("[ADMIN] Could not extract history from follow-up prompt");
          throw new Error("Failed to parse conversation history");
        }
        
        const conversationParts = historyPart.trim().split('\n\n').filter(Boolean);
        console.log(`[ADMIN] Found ${conversationParts.length} conversation parts`);
        
        // Build the messages array with the conversation history
        messages = [
          {
            role: "system",
            content: systemInstruction
          }
        ];
        
        // Add each Q&A pair from the history
        for (const conv of conversationParts) {
          const parts = conv.split('\nResponse: ');
          if (parts.length === 2) {
            const question = parts[0].replace('Question: ', '').trim();
            const answer = parts[1].trim();
            
            if (question && answer) {
              messages.push({ role: "user", content: question });
              messages.push({ role: "assistant", content: answer });
              console.log(`[ADMIN] Added Q&A pair - Q: ${question.substring(0, 30)}... A: ${answer.substring(0, 30)}...`);
            }
          }
        }
        
        // Add the current question
        messages.push({ role: "user", content: actualQuestion });
        console.log(`[ADMIN] Added current question: ${actualQuestion}`);
        console.log(`[ADMIN] Total messages in conversation: ${messages.length}`);
      } catch (parseError) {
        console.error("[ADMIN] Error parsing follow-up conversation:", parseError);
        // Fallback to basic prompt if parsing fails
        messages = [
          { role: "system", content: systemInstruction },
          { role: "user", content: userPrompt.split('\n')[0] } // Just use the first line
        ];
      }
    } else {
      // Regular (non-follow-up) question
      messages = [
        {
          role: "system",
          content: systemInstruction
        },
        {
          role: "user",
          content: userPrompt
        }
      ];
    }
    
    // Prepare the request payload for OpenRouter
    const payload = {
      model: modelId,
      messages: messages,
      temperature: 0.2,
      max_tokens: 4000
    };
    
    console.log(`[ADMIN] Sending ${messages.length} messages to OpenRouter`);
    
    // Make the API request to OpenRouter
    const startTime = Date.now();
    const response = await fetch(openRouterUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${API_KEY}`
      },
      body: JSON.stringify(payload)
    });
    
    const endTime = Date.now();
    const responseTime = (endTime - startTime) / 1000; // Convert to seconds
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenRouter API error: ${response.status} ${errorText}`);
    }
    
    const apiResponse = await response.json();
    
    // Extract the analysis from the response
    let analysis = "";
    if (apiResponse.choices && apiResponse.choices.length > 0) {
      analysis = apiResponse.choices[0].message.content;
    }
    
    if (!analysis) {
      throw new Error(`No analysis was generated by ${modelId}`);
    }
    
    // Calculate token usage - OpenRouter provides usage information
    const tokenInfo = {
      inputTokens: apiResponse.usage?.prompt_tokens || 0,
      outputTokens: apiResponse.usage?.completion_tokens || 0,
      totalTokens: apiResponse.usage?.total_tokens || 0,
      responseTime: responseTime.toFixed(2)
    };
    
    console.log(`[ADMIN] ${modelId} analysis completed successfully. Token usage:`, tokenInfo);
    
    return { 
      analysis, 
      tokenInfo
    };
  } catch (error) {
    console.error(`[ADMIN] Error calling OpenRouter API:`, error);
    throw error;
  }
}

/**
 * Call OpenAI API directly with specified model for conversation analysis
 */
async function callOpenAIDirectAPI(
  modelId: string, 
  systemInstruction: string, 
  userPrompt: string,
  reasoningEffort: "low" | "medium" | "high" | null = null
) {
  try {
    console.log(`[ADMIN] Calling OpenAI API directly with model: ${modelId}`);
    
    // Check if this is a follow-up question
    const isFollowUp = userPrompt.includes("This is a follow-up question to our previous conversation");
    
    if (isFollowUp) {
      console.log("[ADMIN] Processing as follow-up question with conversation history");
      console.log("[ADMIN] Full follow-up prompt:", userPrompt);
    }
    
    // OpenAI API configuration
    const API_KEY = "********************************************************************************************************************************************************************";
    const openAIUrl = "https://api.openai.com/v1/chat/completions";
    
    // For follow-up questions, we need to properly extract the actual question from the context
    let messages: Array<{role: string; content: string}> = [];
    
    if (isFollowUp) {
      try {
        // Extract the actual question (first line before the context)
        const splitPrompt = userPrompt.split('\n\nThis is a follow-up question');
        const actualQuestion = splitPrompt[0].trim();
        
        // Parse out the conversation history
        const historyPart = userPrompt.split("For context, here's the history:")[1];
        if (!historyPart) {
          console.error("[ADMIN] Could not extract history from follow-up prompt");
          throw new Error("Failed to parse conversation history");
        }
        
        const conversationParts = historyPart.trim().split('\n\n').filter(Boolean);
        console.log(`[ADMIN] Found ${conversationParts.length} conversation parts`);
        
        // Build the messages array with the conversation history
        messages = [
          {
            role: "system",
            content: systemInstruction
          }
        ];
        
        // Add each Q&A pair from the history
        for (const conv of conversationParts) {
          const parts = conv.split('\nResponse: ');
          if (parts.length === 2) {
            const question = parts[0].replace('Question: ', '').trim();
            const answer = parts[1].trim();
            
            if (question && answer) {
              messages.push({ role: "user", content: question });
              messages.push({ role: "assistant", content: answer });
              console.log(`[ADMIN] Added Q&A pair - Q: ${question.substring(0, 30)}... A: ${answer.substring(0, 30)}...`);
            }
          }
        }
        
        // Add the current question
        messages.push({ role: "user", content: actualQuestion });
        console.log(`[ADMIN] Added current question: ${actualQuestion}`);
        console.log(`[ADMIN] Total messages in conversation: ${messages.length}`);
      } catch (parseError) {
        console.error("[ADMIN] Error parsing follow-up conversation:", parseError);
        // Fallback to basic prompt if parsing fails
        messages = [
          { role: "system", content: systemInstruction },
          { role: "user", content: userPrompt.split('\n')[0] } // Just use the first line
        ];
      }
    } else {
      // Regular (non-follow-up) question
      messages = [
        {
          role: "system",
          content: systemInstruction
        },
        {
          role: "user",
          content: userPrompt
        }
      ];
    }
    
    // Prepare the request payload for OpenAI
    // o1 model uses different parameters
    const isO1Model = modelId.startsWith('o1');
    
    // Base payload with common parameters
    const payload: any = {
      model: modelId,
      messages: messages
    };
    
    // Add model-specific parameters
    if (isO1Model) {
      // o1 models use max_completion_tokens instead of max_tokens
      // and don't support temperature
      payload.max_completion_tokens = 4000;
      
      // Add reasoning_effort if specified (only for o1 models)
      if (reasoningEffort) {
        payload.reasoning_effort = reasoningEffort;
        console.log(`[ADMIN] Using reasoning_effort: ${reasoningEffort} for o1 model`);
      }
    } else {
      // Non-o1 models use standard parameters
      payload.max_tokens = 4000;
      payload.temperature = 0.2;
    }
    
    console.log(`[ADMIN] Sending ${messages.length} messages to OpenAI API`);
    
    // Make the API request to OpenAI
    const startTime = Date.now();
    const response = await fetch(openAIUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${API_KEY}`
      },
      body: JSON.stringify(payload)
    });
    
    const endTime = Date.now();
    const responseTime = (endTime - startTime) / 1000; // Convert to seconds
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenAI API error: ${response.status} ${errorText}`);
    }
    
    const apiResponse = await response.json();
    
    // Extract the analysis from the response
    let analysis = "";
    if (apiResponse.choices && apiResponse.choices.length > 0) {
      analysis = apiResponse.choices[0].message.content;
    }
    
    if (!analysis) {
      throw new Error(`No analysis was generated by ${modelId}`);
    }
    
    // Calculate token usage - OpenAI provides usage information
    const tokenInfo = {
      inputTokens: apiResponse.usage?.prompt_tokens || 0,
      outputTokens: apiResponse.usage?.completion_tokens || 0,
      totalTokens: apiResponse.usage?.total_tokens || 0,
      responseTime: responseTime.toFixed(2)
    };
    
    console.log(`[ADMIN] ${modelId} analysis completed successfully. Token usage:`, tokenInfo);
    
    return { 
      analysis, 
      tokenInfo
    };
  } catch (error) {
    console.error(`[ADMIN] Error calling OpenAI API:`, error);
    throw error;
  }
}

/**
 * Legacy direct Gemini API call - kept for reference or future use
 */
async function callGeminiAPI(systemInstruction: string, userPrompt: string) {
  try {
    console.log("[ADMIN] Calling Gemini API directly (legacy method)");
    
    // Check if this is a follow-up question
    const isFollowUp = userPrompt.includes("This is a follow-up question to our previous conversation");
    
    if (isFollowUp) {
      console.log("[ADMIN] Processing as follow-up question");
    }
    
    // Gemini API configuration
    const API_KEY = Reframe.env.GEMINI_API_KEY || "AIzaSyDIPxlOphEVJNdGdeB0Oob-1WQNDOTssmI"; // Fallback to public key if env not set
    const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${API_KEY}`;
    
    // Prepare the request payload for Gemini
    const payload = {
      contents: [
        {
          role: "user",
          parts: [
            { text: userPrompt }
          ]
        }
      ],
      systemInstruction: {
        parts: [
          { text: systemInstruction }
        ]
      },
      generationConfig: {
        temperature: 0.2,
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 4000,
        candidateCount: 1,
        stopSequences: []
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_NONE"
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_NONE"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_NONE"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_NONE"
        }
      ],
      tools: []
    };
    
    // Make the API request to Gemini
    const startTime = Date.now();
    const response = await fetch(geminiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(payload)
    });
    
    const endTime = Date.now();
    const responseTime = (endTime - startTime) / 1000; // Convert to seconds
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Gemini API error: ${response.status} ${errorText}`);
    }
    
    const geminiResponse = await response.json();
    
    // Extract the analysis from the response
    let analysis = "";
    if (geminiResponse.candidates && geminiResponse.candidates.length > 0) {
      if (geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts.length > 0) {
        analysis = geminiResponse.candidates[0].content.parts[0].text;
      }
    }
    
    if (!analysis) {
      throw new Error("No analysis was generated by Gemini");
    }
    
    // Calculate token usage
    const tokenInfo = {
      inputTokens: geminiResponse.usageMetadata?.promptTokenCount || 0,
      outputTokens: geminiResponse.usageMetadata?.candidatesTokenCount || 0,
      totalTokens: (geminiResponse.usageMetadata?.promptTokenCount || 0) + (geminiResponse.usageMetadata?.candidatesTokenCount || 0),
      responseTime: responseTime.toFixed(2)
    };
    
    console.log(`[ADMIN] Gemini analysis completed successfully. Token usage:`, tokenInfo);
    
    return { 
      analysis, 
      tokenInfo
    };
  } catch (error) {
    console.error("[ADMIN] Error calling Gemini API:", error);
    throw error;
  }
}

/**
 * Get all users who have sent messages to a specific coach
 * Returns user list with their last message preview
 */
export const getCoachUsersWithLastMessage = async (coachEmail: string) => {
  try {
    console.log(`[COACH_USERS] Fetching users for coach email: ${coachEmail}`);
    
    // First, get the coach details to find their name
    const coach = await db
      .selectFrom("coaches")
      .select(["name"])
      .where("email", "=", coachEmail)
      .executeTakeFirst();

    if (!coach) {
      throw new Error("Coach not found");
    }

    const coachName = coach.name;
    console.log(`[COACH_USERS] Found coach name: ${coachName}`);

    // Unseen message flags are now fetched directly in the main query below
    // using inline EXISTS sub-queries to avoid additional round-trips.

    // Get all unique users who have sent messages to this coach
    // along with their last message
    const usersWithLastMessage = await db
      .selectFrom("conversation as c1")
      .innerJoin("user", "user.channelId", "c1.channelId")
      .innerJoin("userCoachAttributes as uca", join =>
        join.onRef("uca.channelId", "=", "c1.channelId")
      )
      .select([
        "c1.channelId as channelId",
        "user.name as userName",
        "user.email as userEmail",
        "user.image as userImage",
        "user.currentPlan as userCurrentPlan",
        "user.tags as userTags",
        "c1.content as lastMessageContent",
        "c1.date as lastMessageDate",
        "c1.sender as lastMessageSender",
        "c1.messageType as lastMessageType", // keep temporarily for mapping
        sql<number>`(
          SELECT CASE WHEN cr.seen_by_coach = 0 OR cr.seen_by_coach IS NULL THEN 1 ELSE 0 END
          FROM conversation AS cr
          WHERE cr.channel_id = c1.channel_id
            AND cr.coach_name = ${coachName}
            AND cr.message_type IN ('message', 'summary')
            AND cr.sender = 'user'
          ORDER BY cr.date DESC
          LIMIT 1
        )`.as("hasUnseenRegular"),
        sql<number>`(
          SELECT CASE WHEN ct.seen_by_coach = 0 OR ct.seen_by_coach IS NULL THEN 1 ELSE 0 END
          FROM conversation AS ct
          WHERE ct.channel_id = c1.channel_id
            AND ct.coach_name = ${coachName}
            AND ct.message_type = 'coach_message'
            AND ct.sender = 'user'
          ORDER BY ct.date DESC
          LIMIT 1
        )`.as("hasUnseenThread"),
      ])
      .where("uca.coachName", "=", coachName)
      .where("uca.dataSharingEnabled", "=", 1)
      .where("c1.coachName", "=", coachName)
      .where("c1.messageType", "in", ["message", "summary", "coach_message"])
      .where("c1.date", "=", (eb) =>
        eb.selectFrom("conversation as c2")
          .select([eb.fn.max("c2.date").as("maxDate")])
          .where("c2.channelId", "=", eb.ref("c1.channelId"))
          .where("c2.coachName", "=", coachName)
          .where("c2.messageType", "in", ["message", "summary", "coach_message"]) // Filter for 'message' or 'summary' type in subquery
      )
      .orderBy("c1.date", "desc")
      .execute();

    console.log(`[COACH_USERS] Found ${usersWithLastMessage.length} users`);

    // Decrypt the last messages
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Encryption key not found");
    }

    const decryptedUsers = usersWithLastMessage.map(user => {
      try {
        let decryptedContent = "";
        if (user.lastMessageContent) {
          try {
            decryptedContent = CryptoJS.AES.decrypt(user.lastMessageContent, secretKey).toString(CryptoJS.enc.Utf8);
            
            if(user.channelId === 548445911){
              console.log("DECRYPTED CONTENT", decryptedContent);
            }
            // If decryption results in empty string, might be unencrypted legacy message
            if (!decryptedContent && user.lastMessageContent) {
              decryptedContent = user.lastMessageContent;
            }
          } catch (decryptError) {
            console.warn(`[COACH_USERS] Failed to decrypt message for channel ${user.channelId}`);
            decryptedContent = "[Unable to decrypt message]";
            console.error(`[COACH_USERS] Failed to decrypt message for channel ${user.channelId}:`, decryptError);
          }
        }

        const hasUnseenRegular = Boolean((user as any).hasUnseenRegular);
        const hasUnseenThread = Boolean((user as any).hasUnseenThread);

        return {
          channelId: user.channelId,
          userName: user.userName || `User ${user.channelId}`,
          userCurrentPlan: user.userCurrentPlan,
          userEmail: user.userEmail,
          userImage: user.userImage,

          hasUnseen: {
            regularMessage: hasUnseenRegular,
            userThreadMessage: hasUnseenThread,
          },
          tags: (user.userTags ? (user.userTags as string).split(",").filter(Boolean) : []),
          lastMessage: {
            content: decryptedContent,
            date: user.lastMessageDate,
            sender: user.lastMessageSender,
            type: user.lastMessageType
          }
        };
      } catch (error) {
        console.error(`[COACH_USERS] Error processing user ${user.channelId}:`, error);
        return null;
      }
    }).filter(user => user !== null);

    return {
      coachName,
      users: decryptedUsers,
      totalUsers: decryptedUsers.length
    };
  } catch (error) {
    console.error("[COACH_USERS] Error fetching coach users:", error);
    throw error;
  }
};

/**
 * Get all messages between a specific user and coach
 */
export const getCoachUserConversation = async (
  channelId: string | number,
  coach: {
    name: string;
    email: string;
    description: string;
    metadata: string;
  }, 
  limit: number = 50,
  beforeDate?: string
) => {
  // Verify the user is authorized
  // const user = await getSession();
  // if (!user) {
  //   throw new Error("Unauthorized: Authentication required");
  // }

  // // Check if user is superuser or the coach themselves
  // const isSuperUser = SUPERUSER_EMAILS.includes(user.email);
  // const isCoach = user.email === coach.email;
  
  // if (!isSuperUser && !isCoach) {
  //   throw new Error("Unauthorized: You can only view your own conversations");
  // }

  try {
    console.log(`[COACH_CONVERSATION] Fetchingggg conversation for coach ${coach.name} and user ${channelId}`);
    
    console.log("COACH EMAIL", coach.email);

    if (!coach) {
      throw new Error("Coach not found");
    }

    const coachName = coach.name;

    // Ensure the user has enabled data sharing with this coach
    const sharingPref = await db
      .selectFrom("userCoachAttributes")
      .select(["dataSharingEnabled"])
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .executeTakeFirst();

    if (!sharingPref || sharingPref.dataSharingEnabled !== 1) {
      console.log(`[COACH_CONVERSATION] Data sharing disabled for user ${channelId} and coach ${coachName}`);
      return [];
    }

    // Build the query
    let query = db
      .selectFrom("conversation")
      .selectAll()
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .where("messageType", "in", ["message", "summary", "coach_message"])
      .orderBy("date", "desc")
      .limit(limit);

    // Add date filter if provided
    if (beforeDate) {
      query = query.where("date", "<", beforeDate);
    }

    const messages = await query.execute();
    console.log(`[COACH_CONVERSATION] Retrieved ${messages.length} messages`);

    // Decrypt messages
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Encryption key not found");
    }

    const decryptedMessages = messages.map(message => {
      try {
        let decryptedContent = "";
        if (message.content) {
          try {
            decryptedContent = CryptoJS.AES.decrypt(message.content, secretKey).toString(CryptoJS.enc.Utf8);
            
            if (!decryptedContent && message.content) {
              decryptedContent = message.content;
            }
          } catch (decryptError) {
            console.warn(`[COACH_CONVERSATION] Failed to decrypt message ${message.id}`);
            decryptedContent = "[Unable to decrypt message]";
          }
        }

        return {
          id: message.id,
          channelId: message.channelId,
          sender: message.sender,
          content: decryptedContent,
          date: message.date,
          status: message.status,
          messageType: message.messageType,
          audio: message.audio,
          coachName: message.coachName,
          seenByCoach: message.seenByCoach
        };
      } catch (error) {
        console.error(`[COACH_CONVERSATION] Error processing message ${message.id}:`, error.message);
        return null;
      }
    }).filter(message => message !== null);

    // Return in chronological order (oldest first)
    return decryptedMessages.reverse();
  } catch (error) {
    console.error("[COACH_CONVERSATION] Error fetching conversation:", error);
    throw error;
  }
};

/**
 * Get coach details by email
 */
export const getCoachByEmail = async (email: string) => {
  try {
    const coach = await db
      .selectFrom("coaches")
      .select(["id", "name", "email", "description", "type"])
      .where("email", "=", email)
      .executeTakeFirst();

    return coach || null;
  } catch (error) {
    console.error("[GET_COACH_BY_EMAIL] Error:", error);
    return null;
  }
};

/**
 * Generate a summary for a single user's conversation (last 2 weeks)
 * Returns the AI-generated summary and token usage info
 */
export const summarizeUserConversation = async (
  channelId: string | number,
  coachName: string,
  model:
    | 'gemini-2.5-pro-preview-06-05'
    | 'openai-openrouter'
    | 'openai-api-4.5'
    | 'openai-api-o1-medium'
    | 'openai-api-o1-high' = 'gemini-2.5-pro-preview-06-05',
) => {
  try {
    // First, find the most recent message from this user to this coach
    const mostRecentMessage = await db
      .selectFrom('conversation')
      .select(['date'])
      .where('channelId', '=', Number(channelId))
      .where('coachName', '=', coachName)
      .where('messageType', '=', 'message')
      .orderBy('date', 'desc')
      .limit(1)
      .executeTakeFirst();

    if (!mostRecentMessage) {
      throw new Error('No messages found for this user and coach');
    }

    // Calculate 2 weeks back from the most recent message
    const mostRecentMessageDate = new Date(mostRecentMessage.date);
    const twoWeeksBeforeLatest = new Date(mostRecentMessageDate.getTime() - 14 * 24 * 60 * 60 * 1000);

    // Fetch messages within the calculated 2-week range
    const recentMessages = await db
      .selectFrom('conversation')
      .selectAll()
      .where('channelId', '=', Number(channelId))
      .where('coachName', '=', coachName)
      .where('messageType', '=', 'message')
      .where('date', '>=', twoWeeksBeforeLatest.toISOString())
      .where('date', '<=', mostRecentMessageDate.toISOString())
      .orderBy('date', 'asc')
      .execute();

    console.log(
      `[ADMIN] Fetched ${recentMessages.length} messages for summary (last 2 weeks) in channel ${channelId}`,
    );

    // Decrypt message contents
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error('Encryption key not found');
    }

    const decryptedMessages = recentMessages.map((msg) => {
      let decryptedContent = '';
      try {
        decryptedContent = CryptoJS.AES.decrypt(msg.content, secretKey).toString(CryptoJS.enc.Utf8);
        if (!decryptedContent && msg.content) {
          // Legacy unencrypted message fallback
          decryptedContent = msg.content;
        }
      } catch (err) {
        console.warn(`[ADMIN] Failed to decrypt message ${msg.id}`);
        decryptedContent = '[Unable to decrypt message]';
      }

      return { ...msg, content: decryptedContent };
    });

    // Prepare conversations object expected by analyzeConversations
    const conversations: Record<string, any[]> = {
      [String(channelId)]: decryptedMessages,
    };

    // Use analyzeConversations with coach_summary prompt mode
    const result = await analyzeConversations(conversations, "", {
      mode: 'single',
      model: model,
      promptMode: 'coach_summary',
      coachName: coachName
    });

    // Format the date range and prepend to the summary content
    const startDateFormatted = twoWeeksBeforeLatest.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
    const endDateFormatted = mostRecentMessageDate.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
    
    const formattedSummary = `From ${startDateFormatted} to ${endDateFormatted}\n\n${result.analysis}`;

    // NEW: Save the generated summary as a conversation message of type "summary"
    try {
      await saveMessage(
        channelId,
        "assistant",
        formattedSummary,
        undefined,
        "Default",
        "summary",
        coachName,
      );
    } catch (saveErr) {
      console.error('[ADMIN] Error saving summary message:', saveErr);
    }

    return {
      summary: formattedSummary,
      tokenInfo: result.tokenInfo,
      dateRange: {
        startDate: twoWeeksBeforeLatest.toISOString(),
        endDate: mostRecentMessageDate.toISOString(),
      },
    };
  } catch (error) {
    console.error('[ADMIN] Error generating user conversation summary:', error);
    throw error;
  }
};

/**
 * Send a direct message from the coach to a user. The message will be stored
 * in the conversation table with the message_type set to "coach_message".
 * The caller must be either the coach themselves (matching email) or a
 * super-user.
 */
// -------------------
// Tag management
// -------------------

export const updateUserTags = async (coachName: string, channelId: string, tags: string[]) => {
  const user = await getUserInfo({ channelId });
  console.log("user IN UPDATE TAGS", user);
  console.log("tags IN UPDATE TAGS", user?.userTags);
  if (!user) throw new Error("User not found");

  // Authorization: only superuser or assigned coach can update tags
  const isSuper = SUPERUSER_EMAILS.includes(user.email);
  if (!isSuper) {
    const attr = await db
      .selectFrom("user_coach_attributes")
      .select("channel_id")
      .where("channel_id", "=", channelId)
      .where("coachName", "=", coachName)
      .executeTakeFirst();
    if (!attr) throw new Error("Forbidden: Coach does not have access to this user.");
  }

  // Normalize, clean, and deduplicate incoming tags
  const cleanTags = Array.from(
    new Set(tags.map(t => t.trim()).filter(t => t && t !== "[]"))
  );

  // Get existing tags from the denormalized column
  const existingTags = user.userTags;

  const toAdd = cleanTags.filter(t => !existingTags.includes(t));
  const toRemove = existingTags.filter(t => !cleanTags.includes(t));

  // Use a transaction to ensure atomicity
  await db.transaction().execute(async (trx) => {
    if (toAdd.length > 0) {
      await trx
        .insertInto("user_tags")
        .values(toAdd.map(tag => ({ channelId: channelId, tag })))
        .execute();
    }

    if (toRemove.length > 0) {
      await trx
        .deleteFrom("user_tags")
        .where("channelId", "=", channelId)
        .where("tag", "in", toRemove)
        .execute();
    }
  });

  return cleanTags;
};

export const sendCoachMessage = async (
  channelId: string | number,
  coach: { name: string; email: string },
  content: string,
) => {
  // Auth:
  const sessionUser = await getSession();
  if (!sessionUser) {
    throw new Error("Unauthorized: authentication required");
  }

  const isSuperUser = SUPERUSER_EMAILS.includes(sessionUser.email);
  const isCoach = sessionUser.email === coach.email;
  if (!isSuperUser && !isCoach) {
    throw new Error("Unauthorized: only the coach or a super-user can send direct messages");
  }

  // Guard empty content
  const trimmed = content.trim();
  if (!trimmed) {
    throw new Error("Cannot send an empty message");
  }

  // Persist the message. We deliberately set sender to "coach" so that the
  // client can easily distinguish it from assistant responses.
  const saved = await saveMessage(
    channelId,
    "coach",
    trimmed,
    new Date().toISOString(),
    "Default",
    "coach_message",
    coach.name,
  );

  // Return a lightweight object that the client can append to its local
  // state without an additional round-trip for decryption.
  return {
    id: saved?.id || "temp-" + Date.now().toString(),
    channelId: Number(channelId),
    sender: "coach",
    content: trimmed,
    date: new Date().toISOString(),
    status: "Default",
    messageType: "coach_message",
    audio: null,
    coachName: coach.name,
  };
};

// Mark REGULAR conversation messages (message and summary) as seen by coach
export const markConversationMessagesSeenByCoach = async (
  channelId: string | number,
  coachName: string
) => {
  try {
    await db
      .updateTable("conversation")
      .set({ seenByCoach: 1 })
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .where("sender", "=", "user")
      .where("message_type", "in", ["message", "summary"])
      .where((eb) => eb.or([
        eb("seenByCoach", "=", 0),
        eb("seenByCoach", "is", null),
      ]))
      .execute();
    console.log(`[ADMIN] Marked CONVERSATION messages seen for channel ${channelId}`);
  } catch (error) {
    console.error("[ADMIN] Error marking conversation messages seen:", error);
  }
};

// Mark THREAD (coach_message) messages as seen by coach
export const markThreadMessagesSeenByCoach = async (
  channelId: string | number,
  coachName: string
) => {
  try {
    await db
      .updateTable("conversation")
      .set({ seenByCoach: 1 })
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .where("sender", "=", "user")
      .where("message_type", "=", "coach_message")
      .where((eb) => eb.or([
        eb("seenByCoach", "=", 0),
        eb("seenByCoach", "is", null),
      ]))
      .execute();
    console.log(`[ADMIN] Marked THREAD messages seen for channel ${channelId}`);
  } catch (error) {
    console.error("[ADMIN] Error marking thread messages seen:", error);
  }
};

// Legacy (kept for compatibility): mark all messages as seen
export const markMessagesSeenByCoach = async (
  channelId: string | number,
  coachName: string
) => {
  try {
    await db
      .updateTable("conversation")
      .set({ seenByCoach: 1 })
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .where("sender", "=", "user")
      .where(eb => eb("seenByCoach", "=", 0).or("seenByCoach", "is", null))
      .execute();
    console.log(`[ADMIN] Marked messages as seen for channel ${channelId} and coach ${coachName}`);
  } catch (error) {
    console.error("[ADMIN] Error marking ALL messages as seen:", error);
  }
}; 