import { User } from "../db/types.ts";
import { createClient } from "../db/client.ts";
import type { Selectable } from "npm:kysely";

export const db = createClient();

export const getUserInfo = async ({
  email,
  channelId,
}: {
  email?: string;
  channelId?: string;
}): Promise<Selectable<User> | undefined> => {
  let query = db
    .selectFrom("user")
    .select([
      "channelId",
      "name",
      "email",
      "image",
      "createdAt",
      "verifiedToken",
      "onboarding",
      "firstMessage",
      "profileInitiated",
      "profileText",
      "userTags",
    ]);

  if (email) {
    query = query.where("email", "=", email);
  } else if (channelId) {
    const channelIdNumber = Number(channelId);
    if (isNaN(channelIdNumber)) {
      return undefined; // Invalid channel ID
    }
    query = query.where("channelId", "=", channelIdNumber);
  } else {
    // neither email nor channelId provided
    return undefined;
  }

  const user = await query.executeTakeFirst();
  console.log("USER IN GET USER INFO", user);

  if (!user) {
    return undefined;
  }

  const userInfo: Selectable<User> = {
    channelId: user.channelId,
    name: user.name,
    email: user.email,
    image: user.image,
    createdAt: user.createdAt,
    verifiedToken: user.verifiedToken,
    onboarding: user.onboarding,
    firstMessage: user.firstMessage,
    profileInitiated: user.profileInitiated,
    profileText: user.profileText,
    userTags: user.userTags
  };

  // console.log("getUserInfo", email, channelId, userInfo);

  return userInfo;
};
