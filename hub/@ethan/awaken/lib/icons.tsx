import React from "npm:react@canary";
import createIcon from "@reframe/icons/create-icon.ts";

export const BackIcon = createIcon("Back", [
  ["path", {
    d: "M15 6L9 12L15 18",
    stroke: "currentColor", 
    strokeWidth: "2",
    fill: "none",
    key: "back1"
  }],
]);

export const SpeakerIcon = createIcon("Speaker", [
  // Speaker body
  ["path", {
    d: "M11 5L6 9H2V15H6L11 19V5",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    fill: "none",
    key: "speaker1"
  }],
  // Audio waves - three curved lines
  ["path", {
    d: "M15 8C16.6569 9.65685 16.6569 14.3431 15 16",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    fill: "none",
    key: "wave1"
  }],
  ["path", {
    d: "M18 5C21.3137 8.31371 21.3137 15.6863 18 19",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    fill: "none",
    key: "wave2"
  }],
]);

export const SpeakerMutedIcon = createIcon("SpeakerMuted", [
  // Speaker body
  ["path", {
    d: "M11 5L6 9H2V15H6L11 19V5",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    fill: "none",
    key: "mutedSpeaker1"
  }],
  // X mark - moved closer to speaker
  ["line", {
    x1: "20",
    y1: "9",
    x2: "14",
    y2: "15",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    key: "mutedX1"
  }],
  ["line", {
    x1: "14",
    y1: "9",
    x2: "20",
    y2: "15",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    key: "mutedX2"
  }],
]);

export const SquareMessageIcon = createIcon("SquareMessage", [
  ["path", {
    d: "M4 17h6l4 4v-4h6c1.1 0 2-0.9 2-2V5c0-1.1-0.9-2-2-2H4c-1.1 0-2 0.9-2 2v10c0 1.1 0.9 2 2 2z",
    key: "chat1",
    strokeWidth: "1.2"
  }],
]);

// MicOffIcon
export const MicMutedIcon = createIcon("MicMuted", [
  // Mic body (with a break in the path for the strike-through)
  [
    "path",
    {
      d: "M15 9.34V5a3 3 0 0 0-5.94-.6",
      key: "1o9qu4",
    },
  ],
  [
    "path",
    {
      d: "M9 9v3a3 3 0 0 0 5.12 2.12",
      key: "1viy3r",
    },
  ],
  // Bottom lines
  ["path", { d: "M19 10v2a7 7 0 0 1-14 0v-2", key: "1vc78b" }],
  ["line", { x1: "12", x2: "12", y1: "19", y2: "22", key: "x3vr5v" }],
  // Diagonal strike-through line
  ["line", { x1: "2", y1: "2", x2: "22", y2: "22", key: "11kh81" }],
])

export const StarIcon = createIcon("Star", [
  ["polygon", {
    points: "12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26",
    stroke: "currentColor",
    strokeWidth: "1.5",
    key: "star"
  }],
]);

export const SparklesIcon = createIcon("Sparkles", [
  ["path", { d: "m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z", key: "1"}],
]);

export const ThreeSparklesIcon = createIcon("ThreeSparkles", [
  // Large sparkle (right side)
  ["path", { 
    d: "m16 6-1.2 3.6a1.2 1.2 0 0 1-0.8 0.8L10.4 12l3.6 1.2a1.2 1.2 0 0 1 0.8 0.8L16 18l1.2-3.6a1.2 1.2 0 0 1 0.8-0.8L21.6 12l-3.6-1.2a1.2 1.2 0 0 1-0.8-0.8L16 6Z", 
    key: "sparkle1",
    fill: "currentColor"
  }],
  // Small sparkle (top left)
  ["path", { 
    d: "m6 3-0.4 1.2a0.4 0.4 0 0 1-0.4 0.4L3.8 5l1.2 0.4a0.4 0.4 0 0 1 0.4 0.4L6 7l0.4-1.2a0.4 0.4 0 0 1 0.4-0.4L8.2 5l-1.2-0.4a0.4 0.4 0 0 1-0.4-0.4L6 3Z", 
    key: "sparkle2",
    fill: "currentColor"
  }],
  // Small sparkle (bottom left)
  ["path", { 
    d: "m6 17-0.4 1.2a0.4 0.4 0 0 1-0.4 0.4L3.8 19l1.2 0.4a0.4 0.4 0 0 1 0.4 0.4L6 21l0.4-1.2a0.4 0.4 0 0 1 0.4-0.4L8.2 19l-1.2-0.4a0.4 0.4 0 0 1-0.4-0.4L6 17Z", 
    key: "sparkle3",
    fill: "currentColor"
  }],
]);

export const LogOutIcon = createIcon("LogOut", [
  ["path", { d: "M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4", key: "1wkif3" }],
  ["polyline", { points: "16 17 21 12 16 7", key: "1gabdz" }],
  ["line", { x1: "21", x2: "9", y1: "12", y2: "12", key: "1uyos4" }],
]);

export const MessageIcon = createIcon("Message", [
  ["path", { d: "M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z", key: "1"}],
]);

export const UserIcon = createIcon("User", [
  ["path", { d: "M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2", key: "1" }],
  ["circle", { cx: "12", cy: "7", r: "4", key: "2" }],
]);

export const ThreadIcon = createIcon("MessageSquareText", [
  ["path", { d: "M12 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2", key: "1" }],
  ["circle", { cx: "9", cy: "7", r: "4", key: "2" }],
  ["path", { d: "M17 7h4", key: "3" }],
  ["path", { d: "M17 11h4", key: "4" }],
  ["path", { d: "M17 15h2", key: "5" }],
]);

export const CrownIcon = createIcon("Crown", [
  ["path", { d: "M2 4l3 12h14l3-12-6 7-4-7-4 7-6-7z", key: "1" }],
  ["path", { d: "M5 20h14", key: "2" }],
]);

export const CalendarIcon = createIcon("Calendar", [
  ["rect", { x: "3", y: "4", width: "18", height: "18", rx: "2", ry: "2", key: "1" }],
  ["line", { x1: "16", y1: "2", x2: "16", y2: "6", key: "2" }],
  ["line", { x1: "8", y1: "2", x2: "8", y2: "6", key: "3" }],
  ["line", { x1: "3", y1: "10", x2: "21", y2: "10", key: "4" }],
]);

export const PhoneIcon = createIcon("Phone", [
  ["path", { d: "M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z", key: "1" }],
]);

export const DatabaseIcon = createIcon("Database", [
  ["ellipse", { cx: "12", cy: "5", rx: "9", ry: "3", key: "1"}],
  ["path", { d: "M3 5V19A9 3 0 0 0 21 19V5", key: "2"}],
  ["path", { d: "M3 12A9 3 0 0 0 21 12", key: "3"}],
]);

export const EditIcon = createIcon("Edit", [
  ["path", { d: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7", key: "1"}],
  ["path", { d: "M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z", key: "2"}],
]);

export const TrashIcon = createIcon("Trash", [
  ["path", { d: "M3 6h18", key: "1"}],
  ["path", { d: "M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6", key: "2"}],
  ["path", { d: "M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2", key: "3"}],
]);

export const PlusIcon = createIcon("Plus", [
  ["line", { x1: "12", y1: "5", x2: "12", y2: "19", key: "1" }],
  ["line", { x1: "5", y1: "12", x2: "19", y2: "12", key: "2" }],
]);

export const BellIcon = ({ size = 24, color = "#FCA311" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 8C18 6.4087 17.3679 4.88258 16.2426 3.75736C15.1174 2.63214 13.5913 2 12 2C10.4087 2 8.88258 2.63214 7.75736 3.75736C6.63214 4.88258 6 6.4087 6 8C6 15 3 17 3 17H21C21 17 18 15 18 8ZM12 18C8.68629 18 6 15.3137 6 12C6 8.68629 8.68629 6 12 6C15.3137 6 18 8.68629 18 12C18 15.3137 15.3137 18 12 18ZM11 1H13V4H11V1ZM11 20H13V23H11V20ZM3.51472 4.92893L4.92893 3.51472L7.05025 5.63604L5.63604 7.05025L3.51472 4.92893ZM16.9497 18.364L18.364 16.9497L20.4853 19.0711L19.0711 20.4853L16.9497 18.364ZM19.0711 3.51472L20.4853 4.92893L18.364 7.05025L16.9497 5.63604L19.0711 3.51472ZM5.63604 16.9497L7.05025 18.364L4.92893 20.4853L3.51472 19.0711L5.63604 16.9497ZM23 11V13H20V11H23ZM4 11V13H1V11H4Z", key: "1"}
    />
    <path
      d="M13.73 21C13.5542 21.3031 13.3018 21.5545 12.9982 21.7295C12.6946 21.9044 12.3504 21.9965 12 21.9965C11.6496 21.9965 11.3054 21.9044 11.0018 21.7295C10.6982 21.5545 10.4458 21.3031 10.27 21"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SmallBellIcon = createIcon("SmallBell", [
  ["path", { 
    d: "M18 8C18 6.4087 17.3679 4.88258 16.2426 3.75736C15.1174 2.63214 13.5913 2 12 2C10.4087 2 8.88258 2.63214 7.75736 3.75736C6.63214 4.88258 6 6.4087 6 8C6 15 3 17 3 17H21C21 17 18 15 18 8",
    key: "bell1"
  }],
  ["path", { 
    d: "M13.73 21C13.5542 21.3031 13.3018 21.5545 12.9982 21.7295C12.6946 21.9044 12.3504 21.9965 12 21.9965C11.6496 21.9965 11.3054 21.9044 11.0018 21.7295C10.6982 21.5545 10.4458 21.3031 10.27 21",
    key: "bell2"
  }]
]);

export const ArrowLeftIcon = createIcon("ArrowLeft", [
  ["path", { d: "M50 25H5M5 25L25 45M5 25L25 5", key: "1" }],
]);

export const PencilIcon = createIcon("PencilIcon", [
  ["path", { 
    d: "M15.232 5.232L18.768 8.768M16.5 4L12 2L3 11V14.5L4 15.5L8 20L11 21L20 12L16.5 4Z", 
    key: "pencil1",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    fill: "none"
  }],
]);

export const DateSeparator = ({ date }: { date: string }) => {
  return (
    <div className="flex items-center w-full my-4">
      <div className="flex-grow h-px bg-white bg-opacity-20"></div>
      <div className="px-4 py-1 mx-2 rounded-full bg-white bg-opacity-5 border border-white border-opacity-20 text-white text-opacity-70 text-xs">
        {date}
      </div>
      <div className="flex-grow h-px bg-white bg-opacity-20"></div>
    </div>
  );
};

export const MessageCircleIcon = createIcon("MessageCircle", [
  ["path", { d: "M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z", key: "1"}],
]);

export const BrainIcon = createIcon("Brain", [
  ["path", { d: "M9.5 2C8.7 2.3 8 3 8 4V5.5C8 6.4 7.4 7 6.5 7H4C2.9 7 2 7.9 2 9V11C2 11.8 2.5 12.5 3.2 12.8C3.1 13.2 3 13.6 3 14V16C3 17.1 3.9 18 5 18H7.3C7.7 16.8 8.8 16 10 16H12C12.3 16 12.7 16.1 13 16.2V15C13 14.4 13.4 14 14 14H15V13C15 11.9 15.9 11 17 11H18V9C18 7.9 17.1 7 16 7H15.5C14.5 7 13.9 6.2 14 5.2C14.1 4.4 14 3.5 13.5 2.8C13 2.3 12.5 2 12 2H9.5ZM9 4C9 3.4 9.4 3 10 3H12C12.6 3 13 3.4 13 4C13 4.6 12.6 5 12 5H10C9.4 5 9 4.6 9 4ZM6.5 18C5.7 18 5 17.3 5 16.5C5 15.7 5.7 15 6.5 15H10C10.8 15 11.5 15.7 11.5 16.5C11.5 17.3 10.8 18 10 18H6.5ZM9.5 2C8.7 2.3 8 3 8 4V5.5C8 6.4 7.4 7 6.5 7H4C2.9 7 2 7.9 2 9V11C2 11.8 2.5 12.5 3.2 12.8C3.1 13.2 3 13.6 3 14V16C3 17.1 3.9 18 5 18H7.3C7.7 16.8 8.8 16 10 16H12C12.3 16 12.7 16.1 13 16.2V15C13 14.4 13.4 14 14 14H15V13C15 11.9 15.9 11 17 11H18V9C18 7.9 17.1 7 16 7H15.5C14.5 7 13.9 6.2 14 5.2C14.1 4.4 14 3.5 13.5 2.8C13 2.3 12.5 2 12 2H9.5ZM9 4C9 3.4 9.4 3 10 3H12C12.6 3 13 3.4 13 4C13 4.6 12.6 5 12 5H10C9.4 5 9 4.6 9 4ZM6.5 18C5.7 18 5 17.3 5 16.5C5 15.7 5.7 15 6.5 15H10C10.8 15 11.5 15.7 11.5 16.5C11.5 17.3 10.8 18 10 18H6.5Z", key: "1"}],
]);

export const SunIcon = createIcon("Sun", [
  ["path", { d: "M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16ZM12 18C8.68629 18 6 15.3137 6 12C6 8.68629 8.68629 6 12 6C15.3137 6 18 8.68629 18 12C18 15.3137 15.3137 18 12 18ZM11 1H13V4H11V1ZM11 20H13V23H11V20ZM3.51472 4.92893L4.92893 3.51472L7.05025 5.63604L5.63604 7.05025L3.51472 4.92893ZM16.9497 18.364L18.364 16.9497L20.4853 19.0711L19.0711 20.4853L16.9497 18.364ZM19.0711 3.51472L20.4853 4.92893L18.364 7.05025L16.9497 5.63604L19.0711 3.51472ZM5.63604 16.9497L7.05025 18.364L4.92893 20.4853L3.51472 19.0711L5.63604 16.9497ZM23 11V13H20V11H23ZM4 11V13H1V11H4Z", key: "1"}],
]);

export const MoonIcon = createIcon("MoonIcon", [
  ["path", { d: "M10 7C8.89543 7 8 7.89543 8 9C8 10.1046 8.89543 11 10 11C11.1046 11 12 10.1046 12 9C12 7.89543 11.1046 7 10 7ZM10 5C7.79086 5 6 6.79086 6 9C6 11.2091 7.79086 13 10 13C12.2091 13 14 11.2091 14 9C14 6.79086 12.2091 5 10 5ZM11.2071 19.7071L12 19.7071L12 14.7071C12 13.0503 13.3431 11.7071 15 11.7071L20 11.7071L20 10.7071L15 10.7071C12.7909 10.7071 11 12.498 11 14.7071L11 19L11.2071 19.7071ZM19.7929 4.29289L19 4.29289L19 9.29289C19 10.9497 17.6569 12.2929 16 12.2929L11 12.2929L11 13.2929L16 13.2929C18.2091 13.2929 20 11.502 20 9.29289L20 5L19.7929 4.29289Z", key: "1" }]
]);

export const RefreshIcon = createIcon("Refresh", [
  ["path", { d: "M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8", key: "1" }],
  ["path", { d: "M21 3v5h-5", key: "2" }],
  ["path", { d: "M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16", key: "3" }],
  ["path", { d: "M8 16H3v5", key: "4" }],
]);

export const AlertTriangleIcon = createIcon("AlertTriangle", [
  ["path", { d: "M12 9v4m0 4h.01M10.288 3.516L2.13 17.214A2 2 0 003.728 20h16.544a2 2 0 001.598-2.786L13.71 3.516a2 2 0 00-3.422 0z", key: "1"}],
]);

export const PlayIcon = createIcon("Play", [
  ["polygon", { points: "6 3 20 12 6 21 6 3", key: "1" }],
]);

// Alternate slightly wider play icon for program options
export const PlayAltIcon = createIcon("PlayAlt", [
  ["path", { d: "M7 5L17 12L7 19V5Z", key: "playalt1", fill: "currentColor" }],
]);

export const PauseIcon = createIcon("Pause", [
  ["rect", { x: "6", y: "5", width: "4", height: "14", rx: "1", key: "pause1", fill: "currentColor" }],
  ["rect", { x: "14", y: "5", width: "4", height: "14", rx: "1", key: "pause2", fill: "currentColor" }],
]);

export const StopIcon = createIcon("Stop", [
  ["rect", { x: "6", y: "6", width: "12", height: "12", rx: "2", key: "stop1", fill: "currentColor" }],
]);

export const CircleXIcon = createIcon("CircleX", [
  ["circle", { cx: "12", cy: "12", r: "10", key: "circlex1", stroke: "currentColor", strokeWidth: "1.5", fill: "none" }],
  ["path", { d: "M15 9L9 15", key: "circlex2", stroke: "currentColor", strokeWidth: "1.5", strokeLinecap: "round" }],
  ["path", { d: "M9 9L15 15", key: "circlex3", stroke: "currentColor", strokeWidth: "1.5", strokeLinecap: "round" }],
]);

export const MicIcon = createIcon("Mic", [
  ["path", { d: "M12 1C11.2044 1 10.4413 1.31607 9.87868 1.87868C9.31607 2.44129 9 3.20435 9 4V12C9 12.7956 9.31607 13.5587 9.87868 14.1213C10.4413 14.6839 11.2044 15 12 15C12.7956 15 13.5587 14.6839 14.1213 14.1213C14.6839 13.5587 15 12.7956 15 12V4C15 3.20435 14.6839 2.44129 14.1213 1.87868C13.5587 1.31607 12.7956 1 12 1Z", key: "mic1", stroke: "currentColor", strokeWidth: "1.5", fill: "none" }],
  ["path", { d: "M19 10V12C19 13.8565 18.2625 15.637 16.9497 16.9497C15.637 18.2625 13.8565 19 12 19C10.1435 19 8.36301 18.2625 7.05025 16.9497C5.7375 15.637 5 13.8565 5 12V10", key: "mic2", stroke: "currentColor", strokeWidth: "1.5", strokeLinecap: "round", strokeLinejoin: "round", fill: "none" }],
  ["path", { d: "M12 19V23", key: "mic3", stroke: "currentColor", strokeWidth: "1.5", strokeLinecap: "round", strokeLinejoin: "round" }],
  ["path", { d: "M8 23H16", key: "mic4", stroke: "currentColor", strokeWidth: "1.5", strokeLinecap: "round", strokeLinejoin: "round" }],
]);

export const AudioVisualizerIcon = createIcon("AudioVisualizer", [
  ["rect", { x: "4", y: "12", width: "3", height: "5", rx: "1.5", key: "viz1", fill: "currentColor" }],
  ["rect", { x: "9", y: "8", width: "3", height: "13", rx: "1.5", key: "viz2", fill: "currentColor" }],
  ["rect", { x: "14", y: "4", width: "3", height: "17", rx: "1.5", key: "viz3", fill: "currentColor" }],
  ["rect", { x: "19", y: "10", width: "3", height: "9", rx: "1.5", key: "viz4", fill: "currentColor" }],
]);

export const SendIcon = createIcon("Send", [
  ["path", { d: "M22 2L11 13", key: "send1", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" }],
  ["path", { d: "M22 2L15 22L11 13L2 9L22 2Z", key: "send2", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" }],
]);

export const SeedlingIcon = createIcon("Seedling", [
  ["path", { 
    d: "M20.2 13c-.27-.24-2.7-2.3-4.4-2.3-2.1 0-3.1 1.85-3.35 2.82l-.03.15a7.5 7.5 0 0 0-.88.64 6.9 6.9 0 0 0-.77.87l-.06-.25c-.25-.9-.65-1.71-1.08-2.5-.34-.62-.73-1.21-1.13-1.78l.02-.04c.42-.85.79-2.9-.81-4.58-1.28-1.35-4.7-1.72-5.08-1.77L2 4l.07.7c.04.4.41 3.97 1.71 5.33.73.76 1.64 1.17 2.63 1.17.82 0 1.49-.27 1.8-.42a31 31 0 0 1 .98 1.79c.37.77.71 1.58.88 2.38.04.2-.02 7.85-.02 7.85h1.4s-.22-6.32-.18-6.46c.16-.64.52-1.23.99-1.71.23-.23.49-.44.66-.56.4.82 1.41 1.93 3.2 1.93 1.7 0 4.13-2.04 4.4-2.29l.45-.38-.51-.43",
    key: "seedling", 
    fill: "currentColor"
  }]
]);

export const UsersIcon = createIcon("Users", [
  ["path", { d: "M7 21v-2a4 4 0 0 1 4-4h2", key: "1" }],
  ["circle", { cx: "9", cy: "7", r: "4", key: "2" }],
  ["path", { d: "M23 21v-2a4 4 0 0 0-3-3.87", key: "3" }],
  ["circle", { cx: "17", cy: "11", r: "4", key: "4" }],
]);

export const XIcon = createIcon("X", [
  ["line", { x1: "18", y1: "6", x2: "6", y2: "18", key: "1" }],
  ["line", { x1: "6", y1: "6", x2: "18", y2: "18", key: "2" }],
]);

export const ArrowRightIcon = createIcon("ArrowRight", [
  ["path", { d: "M5 12h14", key: "1" }],
  ["polyline", { points: "13 5 20 12 13 19", key: "2" }],
]);

export const ChevronRightIcon = createIcon("ChevronRight", [
  ["polyline", { points: "9 18 15 12 9 6", key: "1" }],
]);

export const EssenceIcon = createIcon("Essence", [
  // Inner flame (brightest part)
  ["path", {
    d: "M12 15c-1.5 0-2.5-1-2.5-2.5 0-1 0.5-2 1.5-3 0.3-0.3 0.6-0.5 0.8-0.7 0.1-0.1 0.2-0.1 0.2-0.1s0.1 0 0.2 0.1c0.2 0.2 0.5 0.4 0.8 0.7 1 1 1.5 2 1.5 3 0 1.5-1 2.5-2.5 2.5z",
    key: "inner",
    fill: "currentColor"
  }],
  // Middle flame
  ["path", {
    d: "M16 16c0 2.2-1.8 4-4 4s-4-1.8-4-4c0-1.5 0.8-3 2-4.5 0.6-0.7 1.2-1.3 1.7-1.8 0.2-0.2 0.3-0.2 0.3-0.2s0.1 0 0.3 0.2c0.5 0.5 1.1 1.1 1.7 1.8 1.2 1.5 2 3 2 4.5z",
    key: "middle",
    fill: "currentColor",
    opacity: "0.6"
  }],
  // Outer flame
  ["path", {
    d: "M12 2c0 0-0.5 0.5-1 1.2-0.8 1-1.8 2.5-2.6 4.2-1.6 3.3-2.4 6.1-2.4 7.6 0 3.3 2.7 6 6 6s6-2.7 6-6c0-1.5-0.8-4.3-2.4-7.6-0.8-1.7-1.8-3.2-2.6-4.2-0.5-0.7-1-1.2-1-1.2z",
    key: "outer",
    fill: "currentColor",
    opacity: "0.3"
  }],
  // Small flickering detail
  ["circle", {
    cx: "9",
    cy: "18",
    r: "1",
    key: "flicker1",
    fill: "currentColor",
    opacity: "0.4"
  }],
  ["circle", {
    cx: "15",
    cy: "17",
    r: "0.5",
    key: "flicker2",
    fill: "currentColor",
    opacity: "0.5"
  }]
]);

export const TagIcon = createIcon("Tag", [
  ["path", { d: "M20.59 13.41L11.17 4a2 2 0 0 0-2.83 0l-5.59 5.59a2 2 0 0 0 0 2.83l9.42 9.42a2 2 0 0 0 2.83 0l5.59-5.59a2 2 0 0 0 0-2.83z", key: "1" }],
  ["circle", { cx: "7.5", cy: "7.5", r: "1.5", key: "2" }],
]);