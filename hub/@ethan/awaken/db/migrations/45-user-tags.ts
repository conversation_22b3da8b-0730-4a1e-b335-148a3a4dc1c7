import { Kysely, sql } from "npm:kysely";

// Helper function to create the tag aggregation logic.
// Using JSON_GROUP_ARRAY is safer than GROUP_CONCAT as it handles special characters
// and is easier to parse in the application layer. It returns '[]' for no tags.
const updateUserTags = (channelIdVar: "'NEW.channel_id'" | "'OLD.channel_id'") => sql`
  UPDATE "user"
  SET tags = (
    SELECT COALESCE(JSON_GROUP_ARRAY(tag), '[]')
    FROM user_tags
    WHERE user_tags.channel_id = ${sql.raw(channelIdVar)}
  )
  WHERE "user".channel_id = ${sql.raw(channelIdVar)};
`;

export async function up(db: Kysely<any>): Promise<void> {
  // 2. Create the user_tags junction table.
  await db.schema
    .createTable("user_tags")
    .addColumn("channel_id", "integer", (col) =>
      col.notNull().references("user.channel_id").onDelete("cascade")
    )
    .addColumn("tag", "text", (col) => col.notNull())
    .addPrimaryKeyConstraint("user_tags_pkey", ["channel_id", "tag"])
    .execute();

  // 3. Create triggers to keep the 'user.tags' column in sync automatically.

  // Trigger for when a new tag is inserted
  await sql`
    CREATE TRIGGER user_tags_after_insert
    AFTER INSERT ON user_tags
    BEGIN
      ${updateUserTags("'NEW.channel_id'")}
    END;
  `.execute(db);

  // Trigger for when a tag is deleted
  await sql`
    CREATE TRIGGER user_tags_after_delete
    AFTER DELETE ON user_tags
    BEGIN
      ${updateUserTags("'OLD.channel_id'")}
    END;
  `.execute(db);
  
  // Trigger for when a tag is updated (e.g., changing the channel_id)
  await sql`
    CREATE TRIGGER user_tags_after_update
    AFTER UPDATE ON user_tags
    BEGIN
      ${updateUserTags("'OLD.channel_id'")}
      ${updateUserTags("'NEW.channel_id'")}
    END;
  `.execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Drop the triggers and tables in the reverse order of creation.
  await sql`DROP TRIGGER IF EXISTS user_tags_after_update;`.execute(db);
  await sql`DROP TRIGGER IF EXISTS user_tags_after_delete;`.execute(db);
  await sql`DROP TRIGGER IF EXISTS user_tags_after_insert;`.execute(db);
  
  await db.schema.dropTable("user_tags").ifExists().execute();

  // Dropping a column in SQLite is complex and often avoided.
  // The common practice is to leave the column or rebuild the table.
  // For a down migration, leaving it is usually acceptable.
  // If you must drop it, you'd need to:
  // 1. CREATE TABLE user_new (...);
  // 2. INSERT INTO user_new SELECT ... FROM user;
  // 3. DROP TABLE user;
  // 4. ALTER TABLE user_new RENAME TO user;
  console.log("The 'tags' column was not dropped from the 'user' table due to SQLite limitations.");
}
